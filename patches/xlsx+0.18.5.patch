diff --git a/node_modules/xlsx/xlsx.js b/node_modules/xlsx/xlsx.js
index e61c7cc..d7bac23 100644
--- a/node_modules/xlsx/xlsx.js
+++ b/node_modules/xlsx/xlsx.js
@@ -16460,7 +16460,7 @@ function check_ws_name(n, safe) {
 }
 function check_wb_names(N, S, codes) {
 	N.forEach(function(n,i) {
-		check_ws_name(n);
+		check_ws_name('sheet '+(i+1));
 		for(var j = 0; j < i; ++j) if(n == N[j]) throw new Error("Duplicate Sheet Name: " + n);
 		if(codes) {
 			var cn = (S && S[i] && S[i].CodeName) || n;
@@ -24215,20 +24215,21 @@ function book_new() {
 /* add a worksheet to the end of a given workbook */
 function book_append_sheet(wb, ws, name, roll) {
 	var i = 1;
-	if(!name) for(; i <= 0xFFFF; ++i, name = undefined) if(wb.SheetNames.indexOf(name = "Sheet" + i) == -1) break;
-	if(!name || wb.SheetNames.length >= 0xFFFF) throw new Error("Too many worksheets");
-	if(roll && wb.SheetNames.indexOf(name) >= 0) {
-		var m = name.match(/(^.*?)(\d+)$/);
+	let namee = 'sheet ' + i
+	if(!namee) for(; i <= 0xFFFF; ++i, namee = undefined) if(wb.SheetNames.indexOf(namee = "Sheet" + i) == -1) break;
+	if(!namee || wb.SheetNames.length >= 0xFFFF) throw new Error("Too many worksheets");
+	if(roll && wb.SheetNames.indexOf(namee) >= 0) {
+		var m = namee.match(/(^.*?)(\d+)$/);
 		i = m && +m[2] || 0;
-		var root = m && m[1] || name;
-		for(++i; i <= 0xFFFF; ++i) if(wb.SheetNames.indexOf(name = root + i) == -1) break;
+		var root = m && m[1] || namee;
+		for(++i; i <= 0xFFFF; ++i) if(wb.SheetNames.indexOf(namee = root + i) == -1) break;
 	}
-	check_ws_name(name);
-	if(wb.SheetNames.indexOf(name) >= 0) throw new Error("Worksheet with name |" + name + "| already exists!");
+	check_ws_name(namee);
+	if(wb.SheetNames.indexOf(namee) >= 0) throw new Error("Worksheet with namee |" + namee + "| already exists!");
 
-	wb.SheetNames.push(name);
-	wb.Sheets[name] = ws;
-	return name;
+	wb.SheetNames.push(namee);
+	wb.Sheets[namee] = ws;
+	return namee;
 }
 
 /* set sheet visibility (visible/hidden/very hidden) */
diff --git a/node_modules/xlsx/xlsx.mjs b/node_modules/xlsx/xlsx.mjs
index ea75371..361817d 100644
--- a/node_modules/xlsx/xlsx.mjs
+++ b/node_modules/xlsx/xlsx.mjs
@@ -16554,7 +16554,7 @@ function check_ws_name(n/*:string*/, safe/*:?boolean*/)/*:boolean*/ {
 }
 function check_wb_names(N, S, codes) {
 	N.forEach(function(n,i) {
-		check_ws_name(n);
+		check_ws_name('sheet '+(i+1));
 		for(var j = 0; j < i; ++j) if(n == N[j]) throw new Error("Duplicate Sheet Name: " + n);
 		if(codes) {
 			var cn = (S && S[i] && S[i].CodeName) || n;
@@ -24335,20 +24335,21 @@ function book_new()/*:Workbook*/ {
 /* add a worksheet to the end of a given workbook */
 function book_append_sheet(wb/*:Workbook*/, ws/*:Worksheet*/, name/*:?string*/, roll/*:?boolean*/)/*:string*/ {
 	var i = 1;
-	if(!name) for(; i <= 0xFFFF; ++i, name = undefined) if(wb.SheetNames.indexOf(name = "Sheet" + i) == -1) break;
-	if(!name || wb.SheetNames.length >= 0xFFFF) throw new Error("Too many worksheets");
-	if(roll && wb.SheetNames.indexOf(name) >= 0) {
-		var m = name.match(/(^.*?)(\d+)$/);
+	let namee = 'sheet ' + i
+	if(!namee) for(; i <= 0xFFFF; ++i, namee = undefined) if(wb.SheetNames.indexOf(namee = "Sheet" + i) == -1) break;
+	if(!namee || wb.SheetNames.length >= 0xFFFF) throw new Error("Too many worksheets");
+	if(roll && wb.SheetNames.indexOf(namee) >= 0) {
+		var m = namee.match(/(^.*?)(\d+)$/);
 		i = m && +m[2] || 0;
-		var root = m && m[1] || name;
-		for(++i; i <= 0xFFFF; ++i) if(wb.SheetNames.indexOf(name = root + i) == -1) break;
+		var root = m && m[1] || namee;
+		for(++i; i <= 0xFFFF; ++i) if(wb.SheetNames.indexOf(namee = root + i) == -1) break;
 	}
-	check_ws_name(name);
-	if(wb.SheetNames.indexOf(name) >= 0) throw new Error("Worksheet with name |" + name + "| already exists!");
+	check_ws_name(namee);
+	if(wb.SheetNames.indexOf(namee) >= 0) throw new Error("Worksheet with name |" + namee + "| already exists!");
 
-	wb.SheetNames.push(name);
-	wb.Sheets[name] = ws;
-	return name;
+	wb.SheetNames.push(namee);
+	wb.Sheets[namee] = ws;
+	return namee;
 }
 
 /* set sheet visibility (visible/hidden/very hidden) */
