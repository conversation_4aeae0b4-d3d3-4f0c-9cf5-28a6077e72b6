# MongoDB Service Guide

## Tổng quan

Dự án FOS eKYC đã được tích hợp với MongoDB thông qua một hệ thống service chung cung cấp các operations CRUD cơ bản. Hệ thống bao gồm:

1. **MongoDBClient** - Class quản lý kết nối MongoDB (singleton pattern)
2. **MongoDBService** - Service cung cấp các operations CRUD chi tiết
3. **DatabaseConnectionService** - Service tích hợp với environment config và cung cấp interface thống nhất

## Cấu hình

### Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/fos_ekyc_dev
MONGODB_DB_NAME=fos_ekyc_dev

# MongoDB Connection Options (optional)
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=5
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
MONGODB_SOCKET_TIMEOUT_MS=45000
MONGODB_CONNECT_TIMEOUT_MS=10000
```

### Khởi tạo kết nối

Kết nối MongoDB được tự động khởi tạo khi start application:

```typescript
import databaseConnectionService from './services/DatabaseConnectionService';

// Kết nối tự động khi start app
await databaseConnectionService.connect();
```

## Sử dụng MongoDB Service

### Import service

```typescript
import databaseConnectionService from '../services/DatabaseConnectionService';
```

### CREATE Operations

#### Insert một document

```typescript
const newUser = {
    name: 'John Doe',
    email: '<EMAIL>',
    age: 30,
    createdAt: new Date()
};

const result = await databaseConnectionService.insert('users', newUser);
console.log('Inserted ID:', result.insertedId);
```

#### Insert nhiều documents

```typescript
const users = [
    { name: 'User 1', email: '<EMAIL>' },
    { name: 'User 2', email: '<EMAIL>' }
];

const result = await databaseConnectionService.insertMany('users', users);
console.log('Inserted count:', result.insertedCount);
```

### READ Operations

#### Tìm một document

```typescript
const user = await databaseConnectionService.findOne('users', { email: '<EMAIL>' });
```

#### Tìm nhiều documents

```typescript
const users = await databaseConnectionService.findMany('users', { age: { $gte: 18 } });
```

#### Tìm với pagination

```typescript
const result = await databaseConnectionService.findWithPagination(
    'users',
    { age: { $gte: 18 } }, // filter
    1, // page
    10, // limit
    { createdAt: -1 } // sort
);

console.log('Data:', result.data);
console.log('Pagination:', result.pagination);
```

#### Đếm documents

```typescript
const count = await databaseConnectionService.count('users', { age: { $gte: 18 } });
```

### UPDATE Operations

#### Update một document

```typescript
const result = await databaseConnectionService.updateOne(
    'users',
    { _id: userId },
    { $set: { name: 'New Name', updatedAt: new Date() } }
);

console.log('Modified count:', result.modifiedCount);
```

#### Update nhiều documents

```typescript
const result = await databaseConnectionService.updateMany(
    'users',
    { age: { $lt: 18 } },
    { $set: { status: 'minor' } }
);
```

#### Upsert (Update hoặc Insert)

```typescript
const result = await databaseConnectionService.upsert(
    'users',
    { email: '<EMAIL>' },
    { $set: { name: 'John Doe', lastLogin: new Date() } }
);
```

### DELETE Operations

#### Delete một document

```typescript
const result = await databaseConnectionService.deleteOne('users', { _id: userId });
console.log('Deleted count:', result.deletedCount);
```

#### Delete nhiều documents

```typescript
const result = await databaseConnectionService.deleteMany('users', { status: 'inactive' });
```

### ADVANCED Operations

#### Aggregation

```typescript
const pipeline = [
    { $match: { age: { $gte: 18 } } },
    { $group: { _id: '$status', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
];

const result = await databaseConnectionService.aggregate('users', pipeline);
```

#### Distinct values

```typescript
const domains = await databaseConnectionService.distinct('users', 'email', {});
```

#### Transaction

```typescript
const result = await databaseConnectionService.withTransaction(async () => {
    // Các operations trong transaction
    await databaseConnectionService.insert('users', newUser);
    await databaseConnectionService.updateOne('counters', { _id: 'users' }, { $inc: { count: 1 } });
    
    return 'Transaction completed';
});
```

## Health Check & Monitoring

### Health Check Endpoints

```bash
GET /api/health              # Basic health check
GET /api/health/detailed     # Detailed system information
GET /api/health/ready        # Kubernetes readiness probe
GET /api/health/live         # Kubernetes liveness probe
```

### Maintenance Endpoints

```bash
GET  /api/maintenance/jobs/status           # Job status
POST /api/maintenance/jobs/run              # Manual maintenance
POST /api/maintenance/jobs/:jobName/control # Control specific job
GET  /api/maintenance/stats/system          # System statistics
```

## Demo API

Để test MongoDB service, có thể sử dụng các demo endpoints:

### User Management Demo

```bash
# Tạo user
POST /api/demo/users
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30
}

# Lấy danh sách users
GET /api/demo/users?page=1&limit=10&search=john

# Lấy user theo ID
GET /api/demo/users/:id

# Cập nhật user
PUT /api/demo/users/:id
{
    "name": "John Smith",
    "age": 31
}

# Xóa user
DELETE /api/demo/users/:id

# Tạo nhiều users
POST /api/demo/users/bulk
{
    "users": [
        { "name": "User 1", "email": "<EMAIL>" },
        { "name": "User 2", "email": "<EMAIL>" }
    ]
}

# Thống kê users
GET /api/demo/users/stats
```

## Best Practices

### 1. Error Handling

```typescript
try {
    const result = await databaseConnectionService.insert('users', userData);
    // Handle success
} catch (error) {
    logger.error('Database operation failed:', error);
    // Handle error
}
```

### 2. Validation

```typescript
// Validate input trước khi insert
if (!userData.name || !userData.email) {
    throw new Error('Name and email are required');
}

// Check duplicate trước khi insert
const existingUser = await databaseConnectionService.findOne('users', { email: userData.email });
if (existingUser) {
    throw new Error('Email already exists');
}
```

### 3. Indexing

```typescript
// Tạo index cho performance
await databaseConnectionService.createIndex('users', { email: 1 }, { unique: true });
await databaseConnectionService.createIndex('users', { createdAt: -1 });
```

### 4. Pagination

```typescript
// Luôn sử dụng pagination cho large datasets
const result = await databaseConnectionService.findWithPagination(
    'users',
    filter,
    page,
    limit,
    sort
);
```

## Troubleshooting

### Connection Issues

1. Kiểm tra MongoDB URI trong environment variables
2. Đảm bảo MongoDB server đang chạy
3. Check network connectivity
4. Xem logs trong console hoặc log files

### Performance Issues

1. Tạo appropriate indexes
2. Sử dụng pagination cho large datasets
3. Optimize aggregation pipelines
4. Monitor memory usage

### Common Errors

- **MongoNetworkError**: MongoDB server không accessible
- **MongoParseError**: Invalid MongoDB URI
- **MongoWriteConcernError**: Write operation failed
- **MongoBulkWriteError**: Bulk operation failed

## Logging

Tất cả database operations đều được log với các levels:

- **INFO**: Successful operations
- **DEBUG**: Query details và performance metrics
- **ERROR**: Failed operations và errors
- **WARN**: Warnings và potential issues

Logs có thể được xem trong console hoặc log files tùy theo cấu hình logger.
