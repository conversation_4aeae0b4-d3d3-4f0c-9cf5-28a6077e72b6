import moment from 'moment';
import logger from '../config/logger';

const VANI_URL = process.env.VANI_URL || "https://affiliate-api.test.vani.la/v1.0/postback"
const VANI_KEY = process.env.VANI_KEY || "dGVEbEh2VWRsdWRrWnJ5aEFaanFhZzpaZjZvU1JiM0NjUVhfbi0zMlJ6UGtlZG1sWmd2R0VESXhia3NpVUFmY1Bj" 
class VaniOpenApiService {  
    public vaniPostback = (data :any) =>
    {
        const host  = VANI_URL    
        data.actionTime =  moment(data.actionTime,"YYYY-MM-DDTHH:mm:ss").utcOffset("+-HH:mm").format() || data.actionTime

        return new Promise((resolve, reject) => {
            try {
                let request = require('request');
                let options = {
                    'method': 'POST',
                    url: host,
                    'headers': {
                        'Authorization': "Basic " + VANI_KEY,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                };
                logger.info(JSON.stringify(data))
                request(options,function (err: any, res: any, body: any){
                    if (err) {
                        return reject({ codeALT: '201', messageErr: err })
                    }
                    logger.info(`==============RES===============\n`)
                    logger.info(res)
                    logger.info(`==============body===============\n`)
                    logger.info(body)
                    logger.info(`==============END===========\n`)
                    try {
                        resolve(res?.statusCode?.toString() || "400")
                    } catch(e) {
                        reject({ codeALT: '210', messageErr: 'API VANI POSTBACK error' })
                    }
                });
            }catch (error){
                return reject({ codeALT: '209', messageErr: error })
            }
        })        
    }
}

// public singleton call api
const vaniOpenApiService = new VaniOpenApiService()
export default vaniOpenApiService
