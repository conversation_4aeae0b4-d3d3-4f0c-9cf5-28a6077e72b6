import path from 'path';
import process from 'process';
import fs from 'fs';
import multiparty from 'multiparty';
import sharp from 'sharp';
import IRequest from 'IRequest';

import axios from 'axios'
import moment from 'moment';
var FormData = require('form-data');


import _, { ceil } from 'lodash'

const errorMessage = 'Invalid request.'

const validateRequest = (fields: any, files: { file: any[] }) => {
  const fileNamePattern = /^\d{10,}$/;
  if (!fields.accNo?.length || !fileNamePattern.test(fields.accNo[0])) {
    throw new Error(errorMessage);
  }

  const file = files.file?.[0];
  if (
      !file?.path ||
      files.file.length !== 1 ||
      !file.headers['content-type'] ||
      !file.headers['content-type'].startsWith('image/')
  ) {
    throw new Error(errorMessage);
  }
}
class AvatarService {
  public uploadAvatar = async (req: IRequest): Promise<any> => {
    const form = new multiparty.Form();
    return await new Promise((resolve, reject) => {
      // tslint:disable-next-line:ter-prefer-arrow-callback
      form.parse(req, async function (err, fields, files) {
        try {
          if (err || !files || !fields) {
            reject(new Error(errorMessage));
            return;
          }

          validateRequest(fields, files);
          const file = files.file[0];
          const fileName = fields.accNo[0];

          const imageBuffer = await sharp(file.path)
              .toFormat('jpeg')
              .toBuffer();

          const folderPathAvatar = process.env.DIR_UPLOAD_AVATAR || 'temp-upload/avatar';
          const folderPath = path.resolve(folderPathAvatar);
          const newPath = path.resolve(`${folderPath}/${fileName}.jpeg`)

          if (!fs.existsSync(folderPath)) {
            reject(new Error(errorMessage));
            return;
          }
          await fs.promises.writeFile(newPath, imageBuffer);

          const imageBase64 = imageBuffer.toString('base64');
          resolve({
            data: `data:image/jpeg;base64,${imageBase64}`,
          });
        } catch (error) {
          reject(error);
          return;
        }
      });
    });
  }

  public getAvatar = async (fileName: string = 'default') :Promise<any> => {
    if (!fileName) {
      return {
        message: 'Invalid request.',
        success: false,
      }
    }

    // tslint:disable-next-line:max-line-length
    const avatarPath = process.env.DIR_UPLOAD_AVATAR || 'temp-upload/avatar';
    const pathUploadAvatar = path.resolve(avatarPath);

    // const avatarPathFile = path.resolve(`${avatarPath}/${fileName}.jpeg`);

    if (!fs.existsSync(pathUploadAvatar)) {
      return {
        message: 'Invalid request.',
        success: false,
      }
    }

    const filenames = fs.readdirSync(pathUploadAvatar);
    let foundFile = filenames.find(file => file.startsWith(fileName));

    if (!foundFile) {
      foundFile = filenames.find(file => file.startsWith('default'));
    }

    const avatarPathFile = path.resolve(`${avatarPath}/${foundFile}`);

    return await new Promise((resolve) => {
      fs.readFile(avatarPathFile, (err, data) => {
        if (err) {
          resolve({
            message: errorMessage,
            success: false,
          })
          return
        }
        resolve({
          message: 'Success',
          success: true,
          data: `data:image/jpeg;base64,${Buffer.from(data).toString('base64')}`,
        })
      });
    });
  }

  public uploadGenerateAvatar = async (req: IRequest): Promise<any> => { 
    const form = new multiparty.Form();
    const { headers } = req;
    return await new Promise((resolve, reject) => {
      form.parse(req, async function (err, fields, files) {
        if (err || !files || !fields) {
          reject(new Error(errorMessage));
          return;
        }
        const file = files.image[0];
        const file_type_temp = _.last(file.originalFilename.split('.')) || ''
        try {
          // cần đánh giá theo dõi
          const formAI = new URLSearchParams();
          // const FormData = require('form-data');
          // const formAI = new FormData();
          // formAI.append('image',  fs.createReadStream(file.path)),
          formAI.append('gender', fields.gender[0] || '')
          formAI.append('style',  fields.style[0] || '')
          if(fields.age != undefined && fields.age.length > 0){
            formAI.append('age',  fields.age[0] || null)
          }

          let imageBuffer = await sharp(file.path).toBuffer();
          let imageBase64 = Buffer.from(imageBuffer).toString('base64')
          const kb = ceil( ( (imageBase64.length * 6) / 8) / 1000 );
          // TH nếu image size > 5mb => thì resize về 1mb (750x750)
          if(kb >= 5000){
            imageBuffer = await sharp(file.path)
            .resize(500)
            .withMetadata()
            .toBuffer();
            imageBase64 = Buffer.from(imageBuffer).toString('base64')
          }

          formAI.append('image_url',  `data:image/${file_type_temp};base64,${imageBase64}`)          
          const config : any= {
              method: 'POST',
              url:     process.env.AVATAR_AI_URL || 'https://testai.shinhansec.com.vn/api/v1/generate/avatar_base64',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',    
                'Lang':  headers.Lang ?? 'VI'
                // ...formAI.getHeaders(),       
                // 'Content-Type': 'multipart/form-data',             
              },
              data: formAI
          }
          const result = await axios.request(config)  
          if(result.status == 200){
            resolve({
              success:true,
              data: result.data,
            });
          }else{
            resolve({success:false, data:null})
          }
        } catch (error) {
          console.log('error: ', error.response);
          reject(error);
        } 
      })      
    });
  }
}

const insAvatarService = new AvatarService()
export default insAvatarService
