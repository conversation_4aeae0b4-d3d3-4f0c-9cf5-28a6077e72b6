import path from 'path';
import httpStatusCodes from 'http-status-codes';
const fs = require('fs');

type TResp ={
    data: any,
    httpStatusCodes: any,
}

class EkycImageService {
    public getImage = (cusNo : string): TResp =>{
        let resp : TResp = {
            data : {},
            httpStatusCodes: null 
        }
        let param = {
            front_id: "",
            back_id: "",
            image_selfie: "",
        }
        if(!cusNo){
            resp.data = { message : "CusNo isn't exist", data: param}
            resp.httpStatusCodes =  httpStatusCodes.OK
            return  resp ;
        }
        
        let messageError = "";
        if(!process.env.DB_LOCAL || !process.env.DB_EKYC || !process.env.LIST_SEC ){
            resp.data = {  message : "Path Folder environment not set", data: param}
            resp.httpStatusCodes =  httpStatusCodes.OK
            return resp;
        }
        const pathFile = process.env.DB_LOCAL.replace("/",'')  + process.env.DB_EKYC + "/"+ process.env.LIST_SEC + "/" +cusNo.toString();
        const pathUser = path.resolve(pathFile);
        if (!fs.existsSync(pathUser)) {
            resp.data = {  message : "Folder Image isn't exist", data: param}
            resp.httpStatusCodes =  httpStatusCodes.OK
            return resp;
        }else{
            const pathFront = "/front_id/front_id.jpg"
            const pathBack = "/back_id/back_id.jpg"
            const pathSelfie = "/video_selfie/image_selfie.jpg"

            if(this.checkImageExist(pathUser,pathFront)){
                const front_id = fs.readFileSync(pathUser + pathFront, { encoding: 'base64'})
                if(front_id){
                    param.front_id = front_id 
                }else{
                    messageError +="Front Image not exist. \n"
                }
            }else{
                messageError +="Folder Front Image not exist. \n"
            }
            if(this.checkImageExist(pathUser,pathBack)){
                const back_id = fs.readFileSync(pathUser + pathBack, { encoding: 'base64'})
                if(back_id){
                    param.back_id = back_id 
                }else{
                    messageError +="Back Image not exist. \n"
                }
            }else{
                messageError +="Folder Back Image not exist. \n"
            }
            if(this.checkImageExist(pathUser,pathSelfie)){
                const image_selfie = fs.readFileSync(pathUser + pathSelfie, { encoding: 'base64'})
                if(image_selfie){
                    param.image_selfie = image_selfie 
                }else{
                    messageError +="Selfie Image Image not exist. \n"
                }
            }else{ 
                messageError += "Folder Selfie Image not exist. \n"
            }

            // if(fs.existsSync(pathUser + pathFront) && fs.statSync(pathUser + pathFront).isFile()){
            //     ///lấy hình ảnh front_id
            //     const front_id = fs.readFileSync(pathUser + pathFront, { encoding: 'base64'})
            //     if(front_id){
            //         param.front_id = front_id 
            //     }else{
            //         messageError +=" Front Image not exist. \n"
            //     }
            // }else{
            //     messageError +=" Folder Front Image not exist. \n"
            // }
            // if(fs.existsSync(pathUser + pathBack) && fs.statSync(pathUser + pathBack).isFile()){
            //     ///lấy hình ảnh back_id
            //     const back_id = fs.readFileSync(pathUser + pathBack, { encoding: 'base64'})
            //     if(back_id){
            //         param.back_id = back_id 
            //     }
            // }else{
            //     messageError +=" Back Image not exist. \n"
            // }
            // if(fs.existsSync(pathUser + pathSelfie) && fs.statSync(pathUser + pathSelfie).isFile()){
            //     ///lấy hình ảnh image_selfie
            //     const image_selfie = fs.readFileSync(pathUser + pathSelfie, { encoding: 'base64'})
            //     if(image_selfie){
            //         param.image_selfie = image_selfie 
            //     }
            // }else{
            //     messageError +=" Selfie Image not exist. \n"
            // }
            resp.data = { data : param, message: messageError }
            resp.httpStatusCodes =  httpStatusCodes.OK
            return resp;
        }
    }  
    checkImageExist = (pathUser:string, pathLink: string):Boolean=>{
        try{
            if(fs.existsSync(pathUser + pathLink) && fs.statSync(pathUser + pathLink).isFile()){
                return true  
            }
            return false
        }catch (e){
            return false
        }
    }
}
// public singleton call api Ekyc Image
const insEkycImageService = new EkycImageService()
export default insEkycImageService
