import moment from 'moment';
import { ICardInfoReq, ICardInfoRes, TImage } from 'vneid';
import logger from '../config/logger';
import path from 'path';
import * as fs from 'fs'
import axios from 'axios';
import * as DATA from '../share/data'


class VneidService {  

    public uploadImage = async (data : TImage) =>
    {   
        /**
         *  filename: <fosid>_<type>_<format>_<time>
         *  timeExcute: thời gian thực thi
         */
        const {fosid, type, format, base64} = data
        const timeExcute = moment().valueOf()
        const filename = `${fosid}_${type}_${timeExcute}.${format}` 
        // xử lý upload - ảnh theo thời gian thực
        // kiểm tra folder
        const dir_upload_img = process.env.DIR_UPLOAD_NEWFOS || 'temp-upload/NewFOS';
        const pathUploadImg = path.resolve(dir_upload_img ); 
        const pathFile = path.resolve(pathUploadImg + '/' + filename);
        logger.info('File: ' + pathFile);
        if (!fs.existsSync(pathUploadImg)) {
            // tạo folder nếu chưa có
            fs.mkdirSync(pathUploadImg, { recursive: true });
        }
        const result = await new Promise ((resolve)=>{
            fs.writeFile(pathFile, base64, 'base64', function(err) {
                if(err){
                    logger.info(err);
                    resolve(false)
                }
                resolve(true)
            });
        })
        if(!result){
            await new Promise ((resolve)=>{
                fs.writeFile(pathFile, base64, 'base64', function(err) {
                    if(err){
                        logger.info('------- Retry ------');
                        logger.info(err);
                    }
                });
            })
            return null
        }
        return filename
    }

    public downloadImage = async (filename : string, fosid: string) =>{
        try {
           // kiểm tra filename có tồn tại ảnh?
           const dir_download_img = process.env.DIR_UPLOAD_NEWFOS || 'temp-upload/NewFOS';
           const pathDownloadImg = path.resolve(dir_download_img); 
           const pathFile = path.resolve(pathDownloadImg + '/' + filename);
           if (!fs.existsSync(pathDownloadImg)) {
             return {
                message: "The folder not exist",
                success: false 
             }
           }
           // lấy image 
            const result = await new Promise((resolve)=>{
                fs.readFile(pathFile, (err, data) => {
                    if (err) {
                        resolve({
                            message: "The Image not exist",
                            success: false 
                        })
                        return
                    }
                    resolve({
                        message: "Successed",
                        success: true,
                        data: Buffer.from(data).toString('base64')
                    })
                });
            })
            return result
        } catch (error) {
            return {
                message: "Server Internal Error",
                success: false
            }
        }
    }

    // API call FPT
    private apiVerifyCardToFPT = (data: any, accessToken:string, parse?: boolean) => {
        const pathUrl= parse ? process.env.NFC_PATH_READ :process.env.NFC_PATH_VERIFY
        console.log('process.env.NFC_DOMAIN: ', process.env.NFC_DOMAIN);

        const option = {
            method: "POST",
            url: process.env.NFC_DOMAIN + pathUrl, 
            port: 443,
            headers: {
                "Content-Type": "application/json",
                "Authorization": 'Bearer ' + accessToken,                
            },
            data: JSON.stringify(data)
        }
        return option
    }

    // GỌI API LOGIN
    private configApiLoginFPT = () => {
        const params = {
            username: process.env.NFC_LOGIN_USR || "altiss_poc",
            password: process.env.NFC_LOGIN_PASS || "altiss@202"
        }
        const option = {
            method: "POST",
            url: process.env.NFC_DOMAIN + process.env.NFC_PATH_LOGIN , 
            port: 443,
            headers: {
                "Content-Type": "application/json",
            },
            data: JSON.stringify(params)
        }
        return option
    }

    // lấy accesstoken
    public GetAccessToken = async (internal?:boolean): Promise <any>=>{
        const seft = this;
        try { 
            const option =  this.configApiLoginFPT();
            logger.info("option: "+ JSON.stringify(option));
            const configAPI : any= {
                method:     option.method,
                url:        option.url,
                headers:    option.headers,
                data:       option.data,
            }
            logger.info("configAPI: "+ JSON.stringify(configAPI) + '\n');
            let result

            if(process.env.NFC_CHECK === 'false'){
                result = DATA.FakeLoginNFC;
            }else {
                result = await axios.request(configAPI)
            }
            logger.info("result: "+ JSON.stringify(result.data) + '\n');
            if(result.status != 200){
                return {
                    message: result.data.message,
                    success: false,
                }
            }
            // thực hiện lưu accesstoken in 
            if(result.data && result.data.hasOwnProperty("accessToken")) {
                const token = result.data.accessToken;
                logger.info("Token save >> " + token);
                const tokenStore = new FptTokenNFC(result.data.accessToken);
                seft.setMemCache(tokenStore);
            }else {
                return {
                    message: "API Refresh accessToken Failed",
                    success: false 
                } 
            }
            return {
                message: "API Refresh accessToken Successed",
                success: true,
                accessToken: internal? result.data.accessToken : null 
            }
        } catch (error) {
            logger.info("error: "+ JSON.stringify(error));
            return {
                message: error.message,
                success: false,
                error: error
            }
        }
    }
    // Xử lý token trên MemoryCache
    private setMemCache = (tokenStore: FptTokenNFC): Promise<any> => {
        console.log('tokenStore: ', tokenStore);
        return new Promise(async (resolve) => {
            try {
                let data = JSON.stringify(tokenStore);
                fs.writeFileSync(pathJsonNFC, data);
                logger.info("Store Token ok");
                return resolve("ok");

            } catch (error) {
                logger.info("Store Token Error");
                logger.info(error);
                resolve(error);
            }
        }).catch(error => {
            logger.info(error);
        })
    }

    private readMemCache = async (): Promise<FptTokenNFC> => {
        // @ts-ignore
        return new Promise(async (resolve) => {
            try {
                if (fs.existsSync(pathJsonNFC)) {
                    fs.readFile(pathJsonNFC,
                        (_err: any, data: any) => {
                            let dataRefresh = JSON.parse(data);
                            logger.info(dataRefresh);
                            if (dataRefresh && dataRefresh.hasOwnProperty('accessToken')) {
                                logger.info("Set value from token cache");
                                resolve(new FptTokenNFC(dataRefresh.accessToken));
                            }
                        });
                } else {
                    resolve(new FptTokenNFC(""));
                }

            } catch (error) {
                logger.info(error);
                resolve(new FptTokenNFC(""));
            }
        }).catch(error => {
            logger.info( "readMemCache: " + JSON.stringify(error));
        })

    }

    // GỌI API READ-NFC
    public ReadNFC = async (input: ICardInfoReq, config: any): Promise<any> =>{
        const seft= this;
        logger.info("\n\n----------------Start ReadNFC VNeID-----------------------");
        try {
            const { idCard, pathUrl} = config;
            let tokenCache = await seft.readMemCache();
            let tokenFptNFC = "";
            if(tokenCache.checkExpire()) {
                // gọi api refresh AccessToken
                const refreshtoken:any = await this.GetAccessToken(true)
                if(refreshtoken.success){
                    tokenFptNFC = refreshtoken.accessToken
                }else{
                    return {
                        success: false,
                        message: "Session is expired"
                    }
                }
            }else {
                tokenFptNFC = tokenCache.getToken()
            }
            if(pathUrl){
                fs.writeFileSync(pathUrl+"/req/"+ moment().format('YYYYMMDD_HHmmss_SSS') +'.json', JSON.stringify(input), 'utf8');
            }
            const option =  this.apiVerifyCardToFPT(input, tokenFptNFC, true)
            logger.info('[option]: ' + JSON.stringify(option)+ '\n');
            const configAPI : any= {
                method:     option.method,
                url:        option.url ,
                headers:    option.headers,
                data:       option.data,
            }
            logger.info('[configAPI]: ' + JSON.stringify(configAPI)+ '\n');
            // gọi api
            let result  
            if(process.env.NFC_CHECK === 'false'){
                result = DATA.FakeReadNFC?.find((el1:any)=>el1.data.data.citizenPid == idCard ) || {}
            }else{
                result = await axios.request(configAPI)
            }
            logger.info('[result]---After send---: ' + JSON.stringify(result.data) + '\n');
            if(pathUrl){
                fs.writeFileSync(pathUrl+"/res/"+ moment().format('YYYYMMDD_HHmmss_SSS') +'.json', JSON.stringify(input), 'utf8');
            }
            if(result.status !=200){
                return {
                    success: false,
                    message: "Call API Read NFC is failed"
                }
            }
            return {
                success: true,
                message: 'API Read NFC is successed',
                data: result.data,
                code: result.data.code
            }
        } catch (error) {
            return {
                success: false,
                message: 'API Read NFC failed',
                error: JSON.stringify(error)
            }
        }
    }

    // GOI API VERIFY-NFC
    public VerifyNFC =  async (input: ICardInfoReq, config: any): Promise<ICardInfoRes> => {
        logger.info("\n\n----------------Start VerifyNFC VNeID-----------------------");
        logger.info('[config]: ' + JSON.stringify(config)+ '\n');
        const seft = this
        const {pathUrl }= config
        try {
            fs.writeFileSync(pathUrl+"/req/"+ moment().format('YYYYMMDD_HHmmss_SSS') +'.json', JSON.stringify(input), 'utf8');
            let tokenCache = await seft.readMemCache();
            let tokenFptNFC = "";
            if(tokenCache.checkExpire()) {
                // gọi api refresh AccessToken
                const refreshtoken:any = await this.GetAccessToken(true)
                if(refreshtoken.success){
                    tokenFptNFC = refreshtoken.accessToken
                }else {
                    return {
                        success: false,
                        message: "Session is expired"
                    }
                }
            }else {
                tokenFptNFC = tokenCache.getToken()
            }

            const option =  this.apiVerifyCardToFPT(input, tokenFptNFC)
            logger.info('[option]: ' + JSON.stringify(option)+ '\n');

            const configAPI : any= {
                method:     option.method,
                url:        option.url,
                headers:    option.headers,
                data:       option.data,
            }
            logger.info('[configAPI]: ' + JSON.stringify(configAPI)+ '\n');
            // gọi API

            // gọi api Verify
            let result
            if(process.env.NFC_CHECK === 'false'){
                result = DATA.FakeVerifyNFC
                logger.info('result: '+ JSON.stringify(result));
            }else{
                result = await axios.request(configAPI)
            }
            logger.info('[result]---After send---: ' + JSON.stringify(result.data) + '\n');
            // save file res
            fs.writeFileSync(pathUrl+"/res/"+ moment().format('YYYYMMDD_HHmmss_SSS') +'.json', JSON.stringify(result.data), 'utf8');
            if(result.status != 200){
                return {
                    success: false,
                    message: "Call API Verify NFC is failed",
                    code: result.status
                }
            }
            if(result.data.exitCode != 0){
                return {
                    success: false,
                    message: "Card Information invalid",
                    code: result.status,
                    exitCode: result.data.exitCode,
                    exitCodeMessage: result.data.exitCodeMessage,
                }
            }
            return {
                success: true,
                message: 'API Verify Card is successed',
                data: result.data.result,
                idCheck: result.data,
            }
        } catch (error) {
            logger.info('error: ' + JSON.stringify(error));
            return {
                success: false,
                message: 'API Verify Card failed',
                error: JSON.stringify(error)
            }
        }
    }
}

// public singleton call api
const vneidService = new VneidService()
export default vneidService


// cấu hình chung
const pathJsonNFC =  path.join(process.cwd(), './JsonAuth/token-nfc.json');
class FptTokenNFC {
    private accessToken: string;

    constructor(accessToken:string) {
        this.accessToken = accessToken;
    }

    public getToken = (): string =>{
        return  this.accessToken;
    }

    public checkExpire = ():boolean =>{
        try {
            logger.info("\n--------> Check Token NFC expire begin <--------");
            const payloadBase64 = this.accessToken.split('.')[1];
            const decodedJson = Buffer.from(payloadBase64, 'base64').toString();
            const decoded = JSON.parse(decodedJson)
            const exp = decoded.exp * 1000;
            let flag = false;
    
            let tokenDate = moment(exp);
    
            if (!tokenDate.isValid())
                flag = true;
    
            let now = moment();
    
            if (now > tokenDate) {
                 logger.info("Token expire >>> " +tokenDate);
                flag = true;
            }
    
            logger.info("--------> Check Token expire result is: "+ flag +" <--------\n");
            return flag;
        } catch (error) {
            logger.info("--------> Check Token expire error is: "+ JSON.stringify(error) +" <--------\n");
            return true
        }
    }
}