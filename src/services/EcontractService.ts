import logger from '../config/logger';
import axios from 'axios'
import moment from 'moment';
import path from 'path';
import * as https from 'https';
const fs = require('fs');
const _ = require("lodash");

const pathJson =  path.join(process.cwd(), './JsonAuth/token.json');

const pathTemplateJson =  path.join(process.cwd(), './JsonAuth/');


const urlLogin =  process.env.URL_LOGIN;

console.log("urlLogin "+urlLogin);

const clientid =   process.env.CLIENT_ID;
const clientsecret =   process.env.CLIENT_SECRET;
const username =   process.env.USERNAME_ECONTRACT;
const password =   process.env.PASSWORD_ECONTRACT;

const urlGetCookie =  process.env.URL_GET_COOKIE;
console.log("urlGetCookie "+urlGetCookie);

const urlGetTemplate =  process.env.URL_GET_TEMPLATE;
console.log("urlGetTemplate "+urlGetTemplate);

const urlExtendDueDate =  process.env.URL_EXTEND_DATE;
console.log("URL_EXTEND_DATE "+urlExtendDueDate);

const urlCheckKeyLinkExit =  process.env.URL_CHECK_KEYLINK;
console.log("URL_CHECK_KEYLINK "+urlCheckKeyLinkExit);


const dataAuth = JSON.stringify({
    "clientid": clientid,
    "clientsecret": clientsecret,
    "username": username,
    "password": password
});

class FptToken {
    private tokenSt: string;
    private expireDt: string;

    constructor(tokenSt:string, expireDt:string) {
        this.tokenSt = tokenSt;
        this.expireDt = expireDt;
    }

    public getToken = ():string =>{
        return  this.tokenSt;
    }

    public checkExpire = ():boolean =>{

         logger.info("***Check Token expire begin****");
        let flag = false;

        let tokenDate = moment(this.expireDt);

        if (!tokenDate.isValid())
            flag = true;

        let now = moment();

        if (now > tokenDate) {
             logger.info("Token expire >>> " +tokenDate);
            flag = true;
        }

         logger.info("***Check Token expire result is >>"+ flag +"<< end****");

        return  flag;
    }
}

class FptTemplateContract  {
    templateId: string
    alias: string
    syncType: any
    datas: AttrA[][]
}

class AttrA {
    id: string
    name: string
    type: string
    value?: string
    owner: string
    dataType: string
    required: boolean
}

class BodyTag {
    customData: CustomDataTag[];
    inputData: InputDataTag;
}

class CustomDataTag {
    recipientId: string;
    email: string;
    telephoneNumber: string;
    contactId: string;
    personalName: string;
    location: string;
    stateOrProvince: string;
    country: string;
    personalID: string;
    passportID: any;
    type: string;
    photoIDCard: any;
    photoIDCardContentType: any;
    photoFrontSideIDCard: string;
    photoFrontSideIDCardContentType: string;
    photoBackSideIDCard: string;
    photoBackSideIDCardContentType: string;
    statusCode: string;
    resourceType: string;
    provideAddress: string;
    provideDate: string;
    commune: string;
    refId: string;
    address: string;
}

class DatasTag {
    id: string;
    name: string;
    type: string;
    value: string;
    owner: string;
    dataType: string;
    required: boolean;
}

class InputDataTag {
    templateId: string;
    alias: string;
    syncType: string;
    datas: DatasTag[][];
}

class FptTemplateGen {
    id: string;
    refId: string;
    selector: string;
    lookup: string;
    attrs: any;
    payload: string;
    body: BodyTag;
}

class AccountNumberSplit {
    hd1: string;
    hd2: string;
    hd3: string;
    hd4: string;
    hd5: string;
    hd6: string;

}


class FptEcontractService {
    public getNewCookie = (infoReq: any): Promise<any> => {
        const seft = this;

        return new Promise(async (resolve) => {
            try {
                let { contactId, envId } = infoReq.body;
                let tokenCache = await seft.readMemCache();
                let tokenFptSt = "";

                let flagExpire = tokenCache.checkExpire();

                if (flagExpire) {

                    const configCall = {
                        method: 'post',
                        url: urlLogin,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: dataAuth
                    };

                    // @ts-ignore
                    await axios(configCall)
                        .then(async function(response: any) {
                            let responseData = response.data;
                            logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 100) + '....<<');

                            if (responseData && responseData.hasOwnProperty("access_token")) {
                                const token = responseData.hasOwnProperty("access_token");
                                logger.info("Token save >> " + token);
                                const tokenStore = new FptToken(responseData.access_token, responseData.expTime);
                                seft.setMemCache(tokenStore);
                                tokenFptSt = responseData.access_token;
                            }
                        })
                        .catch(function(error) {
                            logger.info(error);
                            return resolve({ codeALT: '209', messageErr: error });
                        });
                } else {
                    tokenFptSt = tokenCache.getToken();
                }

                let dataRequestFpt = {
                    "contactId": contactId,
                    "envelopeId": envId
                }

                const agent = new https.Agent({
                    rejectUnauthorized: false,
                    keepAlive: true
                });

                const configCallGetNewCookie = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: urlGetCookie,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + tokenFptSt,
                        'Accept-Encoding': 'gzip, zlibgzip, deflate, compress, br'
                    },
                    timeout: 60000,
                    httpsAgent: agent,
                    data: dataRequestFpt
                };

                let webViewResult = {
                    "contactId": contactId,
                    "envId": envId,
                    "url": "",
                    "cookieName": "",
                    "cookieValue": "",
                    "expireTime": "",
                    "username": "",
                    "password": "",
                    "iframeUrl": ""
                }


                // @ts-ignore
                await axios(configCallGetNewCookie)
                    .then(async function(response: any) {
                        logger.info(JSON.stringify(response.data));
                        let responseData = response.data;
                        if (Array.isArray(responseData)) {

                            let found_env = _.find(responseData, (o: { envId: string; }) => o.envId == envId);

                            if (found_env && found_env.hasOwnProperty('webView')) {

                                let {
                                    url,
                                    cookieName,
                                    cookieValue,
                                    expireTime,
                                    username,
                                    password,
                                    iframeUrl
                                } = found_env.webView;

                                webViewResult.url = url ? url : '';
                                webViewResult.cookieName = cookieName ? cookieName : '';
                                webViewResult.cookieValue = cookieValue ? cookieValue : '';
                                webViewResult.expireTime = expireTime ? expireTime : '';
                                webViewResult.username = username ? username : '';
                                webViewResult.password = password ? password : '';
                                webViewResult.iframeUrl = iframeUrl ? iframeUrl : '';
                            }

                        }
                    })
                    .catch(function(error) {
                        logger.info(error);
                        return resolve({ codeALT: '209', messageErr: error });
                    });

                console.log("CALL FPT end ");

                resolve(webViewResult);
            } catch (error) {
                console.log(error);
                resolve({ codeALT: '209', messageErr: JSON.stringify(error) })
            }

        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        })
    }
    public getSession = (infoReq: any): Promise<any> => {
        const seft = this;

        return new Promise(async (resolve) => {
            try {

                let flagRefeshSession = infoReq.body.flagRefeshSession;

                if (!infoReq || !infoReq.body || !infoReq.body.hasOwnProperty("flagRefeshSession")) {
                    return resolve({ codeALT: '209', messageErr: "Call error param flag" });
                }

                logger.info("flagRefeshSession is " + flagRefeshSession);


                const configCall = {
                    method: 'post',
                    url: urlLogin,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: dataAuth
                };

                let tokenCache = await seft.readMemCache();

                let flagExpire = tokenCache.checkExpire();

                if (!flagExpire && !flagRefeshSession) {
                    resolve({ "token": true });
                } else {

                    // @ts-ignore
                    axios(configCall)
                        .then(async function(response: any) {
                            logger.info(JSON.stringify(response.data));
                            let responseData = response.data;
                            if (responseData && responseData.hasOwnProperty("access_token")) {
                                const token = responseData.hasOwnProperty("access_token");
                                logger.info("Token save >> " + token);
                                const tokenStore = new FptToken(responseData.access_token, responseData.expTime);
                                seft.setMemCache(tokenStore);
                            } else {
                                resolve({ "token": false, "error": "Vendor call fail" });
                            }

                            resolve({ "token": true });
                        })
                        .catch(function(error) {
                            logger.info(error);
                            resolve({ "token": false, "error": error })
                        });
                }

            } catch (error) {
                resolve({ codeALT: '209', messageErr: JSON.stringify(error) })
            }
        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        })
    }
    public getTemplate = (alias: string): Promise<any> => {
        const seft = this;
        let templateFpt = new FptTemplateContract();

        return new Promise(async (resolve) => {
            try {

                let tokenCache = await seft.readMemCache();
                let tokenFptSt = "";

                let flagExpire = tokenCache.checkExpire();

                if (flagExpire) {

                    const configCall = {
                        method: 'post',
                        url: urlLogin,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: dataAuth
                    };

                    // @ts-ignore
                    await axios(configCall)
                        .then(async function(response: any) {
                            let responseData = response.data;
                            logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 100) + '....<<');

                            if (responseData && responseData.hasOwnProperty("access_token")) {
                                const token = responseData.hasOwnProperty("access_token");
                                logger.info("Token save >> " + token);
                                const tokenStore = new FptToken(responseData.access_token, responseData.expTime);
                                seft.setMemCache(tokenStore);
                                tokenFptSt = responseData.access_token;
                            }
                        })
                        .catch(function(error) {
                            logger.info(error);
                            return resolve({ codeALT: '209', messageErr: error });
                        });
                } else {
                    tokenFptSt = tokenCache.getToken();
                }

                const agent = new https.Agent({
                    rejectUnauthorized: false,
                    keepAlive: true
                });

                const configCallGetTemplate = {
                    method: 'get',
                    maxBodyLength: Infinity,
                    url: urlGetTemplate + alias,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + tokenFptSt,
                        'Accept-Encoding': 'gzip, zlibgzip, deflate, compress, br'
                    },
                    timeout: 60000,
                    httpsAgent: agent,
                };

                // @ts-ignore
                await axios(configCallGetTemplate)
                    .then(async function(response: any) {
                        logger.info(JSON.stringify(response.data));
                        let responseData = response.data;
                        if (responseData.hasOwnProperty('templateId')) {
                            templateFpt = responseData;
                        }
                    })
                    .catch(function(error) {
                        logger.info(error);
                        return resolve({ codeALT: '209', messageErr: error });
                    });

                console.log("CALL FPT end ");

                resolve(templateFpt);
            } catch (error) {
                logger.info(error);
                resolve(templateFpt);
            }

        }).catch(error => {
            logger.info(error);
            return templateFpt;
        })
    }
    public getAllTemplate = (infoReq: any): Promise<any> => {
        const seft = this;
        return new Promise(async (resolve) => {
            let alias = "HDMTK04";
            let templateResonse = await this.getTemplate(alias);
            let store = await this.storeTemplate(templateResonse, alias);

            templateResonse = await this.getTemplate(alias);
            alias = "MARGIN";
            await this.storeTemplate(templateResonse, alias);

            templateResonse = await this.getTemplate(alias);
            alias = "HDMTKBS01";
            await this.storeTemplate(templateResonse, alias);


            return resolve({
                success: true
            });
        })
    }

    private setMemCache = (tokenStore: FptToken): Promise<any> => {
        return new Promise(async (resolve) => {
            try {
                let data = JSON.stringify(tokenStore);
                fs.writeFileSync(pathJson, data);
                logger.info("Store Token ok");
                return resolve("ok");

            } catch (error) {
                logger.info("Store Token Error");
                logger.info(error);
                resolve(error);
            }
        }).catch(error => {
            logger.info(error);
        })
    }

    private readMemCache = async (): Promise<FptToken> => {
        // @ts-ignore
        return new Promise(async (resolve) => {
            try {
                if (fs.existsSync(pathJson)) {
                    await fs.readFile(pathJson,
                        (err: any, data: string) => {

                            let dataR = JSON.parse(data);
                            logger.info(dataR);
                            if (dataR && dataR.hasOwnProperty('tokenSt') && dataR.hasOwnProperty('expireDt')) {
                                logger.info("Set value from token cache");
                                resolve(new FptToken(dataR.tokenSt, dataR.expireDt));
                            }
                        });
                } else {
                    resolve(new FptToken("", ""));
                }

            } catch (error) {
                logger.info(error);
                resolve(new FptToken("", ""));
            }
        }).catch(error => {
            logger.info(error);
        })

    }

    private storeTemplate = (template: FptTemplateContract, alias: string): Promise<any> => {
        return new Promise(async (resolve) => {
            try {
                let data = JSON.stringify(template);
                fs.writeFileSync(pathTemplateJson + alias + '.json', data);
                logger.info("Store Template >>>>" + alias + " ok");
                return resolve("ok");

            } catch (error) {
                logger.info("Store Template >>>>" + alias + " error");
                logger.info(error);
                resolve(error);
            }
        }).catch(error => {
            logger.info(error);
        })
    }

    private loadTemplate = async (alias: string): Promise<FptTemplateContract> => {
        let templateFpt = new FptTemplateContract();
        // @ts-ignore
        return new Promise(async (resolve) => {
            try {
                if (fs.existsSync(pathTemplateJson + alias + '.json')) {
                    await fs.readFile(pathTemplateJson + alias + '.json',
                        (err: any, data: string) => {

                            let dataR = JSON.parse(data);
                            logger.info(dataR);
                            dataR = templateFpt;
                            resolve(templateFpt)
                        });
                } else {
                    resolve(templateFpt);
                }

            } catch (error) {
                logger.info(error);
                resolve(templateFpt);
            }
        }).catch(error => {
            logger.info(error);
            return (templateFpt);
        })

    }

    private fillTemplate = async (dataFill: Array<string | number> , template:FptTemplateContract ): Promise<any> => {
        return new Promise(async (resolve) => {
            let linkID = dataFill[0];
            let nationalId = dataFill[1];
            let fileNameFrontNation = dataFill[2];
            let fileNameBackNation = dataFill[3];

            let base64fileNameFrontNation = "";
            let base64fileNameBackNation = "";

            let issueDate = moment(dataFill[4], "YYYYMMDD").format("dd/MM/yyyy");
            let issuePlace = dataFill[5];
            let birthDate =  moment(dataFill[6], "YYYYMMDD").format("dd/MM/yyyy");

            let fullName = dataFill[7];
            let mobiPhone = dataFill[8];
            let homePhone = dataFill[9];

            let currentAddress = dataFill[10];
            let contactAddress = dataFill[11];
            let fax = dataFill[12];

            let sex = dataFill[13];
            let margin = dataFill[14];

            let accountNumber = dataFill[15];

            resolve('');
        });

    }

    private splitAccountNo = (accountNo:string) : AccountNumberSplit =>{
        let accountNumberSplit =  new AccountNumberSplit();

        return accountNumberSplit;
    }

    public extendDateExpire = (extendDate: number, envID: string): Promise<any> => {
        const seft = this;
        return new Promise(async (resolve) => {
            let extendDateupdate = _.parseInt(extendDate);
            const durationAllow = [15, 20, 30];

            if(!durationAllow.includes(extendDateupdate) || !envID || envID =='') {
                return false;
            }


            let tokenCache = await seft.readMemCache();
            let tokenFptSt = "";

            let flagExpire = tokenCache.checkExpire();

            if (flagExpire) {

                const configCall = {
                    method: 'post',
                    url: urlLogin,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: dataAuth
                };

                // @ts-ignore
                await axios(configCall)
                    .then(async function(response: any) {
                        let responseData = response.data;
                        logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 100) + '....<<');

                        if (responseData && responseData.hasOwnProperty("access_token")) {
                            const token = responseData.hasOwnProperty("access_token");
                            logger.info("Token save >> " + token);
                            const tokenStore = new FptToken(responseData.access_token, responseData.expTime);
                            seft.setMemCache(tokenStore);
                            tokenFptSt = responseData.access_token;
                        }
                    })
                    .catch(function(error) {
                        logger.info(error);
                        return resolve({ codeALT: '209', messageErr: error });
                    });
            } else {
                tokenFptSt = tokenCache.getToken();
            }

            const dataExtend = JSON.stringify({
                "id": "",
                "refId": "",
                "selector": "flow_processing_shinhan_extend_duedates",
                "lookup": "",
                "attrs": {},
                "payload": "",
                "body": {
                    "envelopeId": envID,
                    "extendDate": extendDateupdate.toString()
                }
            });

            logger.info(dataExtend);

            const agent = new https.Agent({
                rejectUnauthorized: false,
                keepAlive: true
            });

            const configCall = {
                method: 'post',
                maxBodyLength: Infinity,
                url: urlExtendDueDate,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + tokenFptSt,
                    'Accept-Encoding': 'gzip, zlibgzip, deflate, compress, br'
                },
                timeout: 60000,
                httpsAgent: agent,
                data: dataExtend
            };

            // @ts-ignore
            axios.request(configCall)
                .then((response) => {
                    logger.info(JSON.stringify(response.data));

                    let {id, code, message} = response.data;

                    if(code == "0"){
                        return resolve({
                            extendDateExpire: true
                        });
                    }

                    return resolve({
                        extendDateExpire: false
                    });


                })
                .catch((error) => {
                    logger.info(error);
                    return resolve({
                        extendDateExpire: false,
                        error:error
                    });
                });


        }).catch(error => {
            logger.info(error);
            return{
                extendDateExpire: false,
                error:error
            };
        })
    }

    public checkKeyLink = (refId: string): Promise<any> => {
        const seft = this;
        return new Promise(async (resolve) => {

            if( !refId || refId =='') {
                return false;
            }


            let tokenCache = await seft.readMemCache();
            let tokenFptSt = "";

            let flagExpire = tokenCache.checkExpire();

            if (flagExpire) {

                const configCall = {
                    method: 'post',
                    url: urlLogin,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: dataAuth
                };

                // @ts-ignore
                await axios(configCall)
                    .then(async function(response: any) {
                        let responseData = response.data;
                        logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 100) + '....<<');

                        if (responseData && responseData.hasOwnProperty("access_token")) {
                            const token = responseData.hasOwnProperty("access_token");
                            logger.info("Token save >> " + token);
                            const tokenStore = new FptToken(responseData.access_token, responseData.expTime);
                            seft.setMemCache(tokenStore);
                            tokenFptSt = responseData.access_token;
                        }
                    })
                    .catch(function(error) {
                        logger.info(error);
                        return resolve({ codeALT: '209', messageErr: error });
                    });
            } else {
                tokenFptSt = tokenCache.getToken();
            }

            const dataExtend = JSON.stringify({
                "id": "",
                "refId": "",
                "selector": "flow_processing_shinhan_get_contract_info",
                "lookup": "",
                "attrs": {},
                "payload": "",
                "body": {
                    "actList": [
                        {
                            "refId": refId.toString()
                        }
                    ]
                }
            });

            logger.info(dataExtend);

            const agent = new https.Agent({
                rejectUnauthorized: false,
                keepAlive: true
            });

            const configCall = {
                method: 'post',
                maxBodyLength: Infinity,
                url: urlCheckKeyLinkExit,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + tokenFptSt,
                    'Accept-Encoding': 'gzip, zlibgzip, deflate, compress, br'
                },
                timeout: 60000,
                httpsAgent: agent,
                data: dataExtend
            };

            // @ts-ignore
            axios.request(configCall)
                .then((responseData) => {
                    logger.info(JSON.stringify(responseData.data));

                    let {id, code, message, response} = responseData.data;

                    if(code == "0"){

                        if(response) {
                            let {envId, envStatus} = response;

                            return resolve({
                                checkKeylink: true,
                                "envId": envId,
                                "envStatus": envStatus
                            });
                        }

                        return resolve({
                            checkKeylink: false,
                            "envId": "",
                            "envStatus": ""
                        });
                    }

                    return resolve({
                        checkKeylink: false,
                        "envId": "",
                        "envStatus": ""
                    });


                })
                .catch((error) => {
                    logger.info(error);
                    return resolve({
                        checkKeylink: false,
                        "envId": "",
                        "envStatus": "",
                        error:error
                    });
                });


        }).catch(error => {
            logger.info(error);
            return{
                checkKeylink: false,
                "envId": "",
                "envStatus": "",
                error:error
            };
        })
    }

}

// public singleton call api Fpt
const insFptEcontractService = new FptEcontractService()
export default insFptEcontractService
