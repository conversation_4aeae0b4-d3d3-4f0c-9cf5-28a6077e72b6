import logger from '../config/logger';
import httpStatusCodes from 'http-status-codes';
import path from 'path';
import errors from '../constants/errors';
import https from 'https';
const fs = require('fs');
const dir_upload_img = process.env.DIR_UPLOAD_IMG || 'temp-upload';
const urlCreateEcontract =  process.env.URL_ECONTRACT_CREATE;
console.log("urlCreatEcontract :"+urlCreateEcontract);
import axios from 'axios';
const _ = require("lodash");

class CopyImageService {
    public uploadToServer(infoReq: any) : Promise<any> {

        return new Promise(async (resolvePr) => {
            try {

                let flagCopy = true;
                let messageErr = '';

                if (!infoReq || !infoReq.body || !infoReq.body.hasOwnProperty('fosNo')|| !infoReq.body.hasOwnProperty('cusNo')) {
                    return resolvePr({ 'upload': false, messageErr: 'Call error param fosNo  ,cusNo not valid' });
                }

                let { fosNo, cusNo } = infoReq.body;

                if(!process.env.DB_LOCAL || !process.env.DB_EKYC || !process.env.LIST_SEC ){

                    return resolvePr({ 'upload': false, messageErr: 'Path Folder environment not set' });
                }
                const pathFile = process.env.DB_LOCAL.replace("/",'')  + process.env.DB_EKYC + "/081/" +fosNo.toString();
                const pathUser = path.resolve(pathFile);

                const pathUploadImg =  path.resolve(dir_upload_img);

                if (!fs.existsSync(pathUser)) {
                    return resolvePr({ 'upload': false, messageErr: 'Folder Image isn\'t exist' });
                }else {
                    const front_img_name ='front_id.jpg';
                    const back_img_name ='back_id.jpg';
                    const self_img_name ='image_selfie.jpg';
                    const video_self_img_name ='video_selfie.mp4';
                    const video_self_img_name_mov ='video_selfie.mov';

                    const target_front_img_name = 'CUST_'+cusNo + '_front_id.jpg';
                    const target_back_img_name =  'CUST_'+cusNo + '_back_id.jpg';
                    const target_self_img_name =  'CUST_'+cusNo + '_image_selfie.jpg';
                    const target_video_self_img_name =  'CUST_'+cusNo + '_video_selfie.mp4';
                    const target_video_self_img_name_mov =  'CUST_'+cusNo + '_video_selfie.mov';

                    const pathFront = "/front_id/"+front_img_name;
                    const pathBack = "/back_id/"+back_img_name;
                    const pathSelfie = "/video_selfie/"+self_img_name;
                    const pathVideoSelfie = "/video_selfie/"+video_self_img_name;
                    const pathVideoSelfieMOV = "/video_selfie/"+video_self_img_name_mov;


                    if(this.checkImageExist(pathUser,pathFront)){
                        await new Promise(async (resolve, reject) => {
                            flagCopy = await this.copyFile(pathUser + pathFront, pathUploadImg + '/' + target_front_img_name);

                            //retry when fail
                            if(!flagCopy) {
                                flagCopy = await this.copyFile(pathUser + pathFront, pathUploadImg + '/' + target_front_img_name);
                            }

                            resolve(flagCopy);

                        })


                    }else{
                        flagCopy = false;
                        messageErr +="Folder Front Image not exist. \n";
                        return  resolvePr({ 'upload': flagCopy, messageErr: messageErr });
                    }

                    if(this.checkImageExist(pathUser,pathBack)){
                        await new Promise(async (resolve, reject) => {
                            flagCopy = await this.copyFile(pathUser + pathBack, pathUploadImg + '/' + target_back_img_name);

                            //retry when fail
                            if(!flagCopy) {
                                flagCopy = await this.copyFile(pathUser + pathBack, pathUploadImg + '/' + target_back_img_name);
                            }

                            resolve(flagCopy);

                        });
                    }else{
                        flagCopy = false;
                        messageErr +="Folder Back Image not exist. \n";
                        return  resolvePr({ 'upload': flagCopy, messageErr: messageErr });
                    }

                    if(this.checkImageExist(pathUser,pathSelfie)){

                        await new Promise(async (resolve, reject) => {
                            flagCopy = await this.copyFile(pathUser + pathSelfie, pathUploadImg + '/' + target_self_img_name);

                            //retry when fail
                            if(!flagCopy) {
                                flagCopy = await this.copyFile(pathUser + pathSelfie, pathUploadImg + '/' + target_self_img_name);
                            }

                            resolve(flagCopy);
                        });
                        if(this.checkImageExist(pathUser,pathVideoSelfie)){
                            await new Promise(async (resolve, reject) => {
                                flagCopy = await this.copyFile(pathUser + pathVideoSelfie, pathUploadImg + '/' + target_video_self_img_name);
    
                                //retry when fail
                                if(!flagCopy) {
                                    flagCopy = await this.copyFile(pathUser + pathVideoSelfie, pathUploadImg + '/' + target_video_self_img_name);
                                }
    
                                resolve(flagCopy);
                            });
                        }
                        if(this.checkImageExist(pathUser,pathVideoSelfieMOV)){
                            await new Promise(async (resolve, reject) => {
                                flagCopy = await this.copyFile(pathUser + pathVideoSelfieMOV, pathUploadImg + '/' + target_video_self_img_name_mov);
    
                                //retry when fail
                                if(!flagCopy) {
                                    flagCopy = await this.copyFile(pathUser + pathVideoSelfieMOV, pathUploadImg + '/' + target_video_self_img_name_mov);
                                }
                                resolve(flagCopy);
                            });
                        }
                    }else{
                        flagCopy = false;
                        messageErr += "Folder Selfie Image not exist. \n";
                        return  resolvePr({ 'upload': flagCopy, messageErr: messageErr });
                    }

                   return  resolvePr({ 'upload': flagCopy, messageErr: messageErr });
                }


            } catch (error) {
                console.log(error);
                return resolvePr({ codeALT: '209', messageErr: JSON.stringify(error) })
            }

        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        });
    }

    checkImageExist = (pathUser:string, pathLink: string):Boolean=>{
        try{
            logger.info("Check file path >>>"+pathUser + pathLink);
            if(fs.existsSync(pathUser + pathLink) && fs.statSync(pathUser + pathLink).isFile()){
                return true
            }
            return false
        }catch (e){
            return false
        }
    }

    copyFile  = async (source: string, target:string) : Promise<boolean> =>{

        try {
            let wr = await fs.createWriteStream(target);
            let rd = await fs.createReadStream(source);

            rd.on("error", function (err: any) {
                logger.info(JSON.stringify(err));

                rd.destroy();
                return false;
            });


            rd.on("close", function (err: any) {
                rd.destroy();
                return false;
            });

            wr.on("error", function (err: any) {
                logger.info(JSON.stringify(err));

                logger.info(' Image >>' + source + ' >>> ' + target + ' error ');
                wr.destroy();

                return false;
            });

            wr.on("close", function (ex: any) {
                logger.info(JSON.stringify(ex));
                logger.info(' Image >>' + source + ' >>> ' + target + ' ok ');
                wr.destroy();

                return true;
            });

            rd.pipe(wr);

            function done(err: any) {
                return false;
            }

        } catch (e) {
            logger.error("Lỗi xãy ra");
            logger.error(e);
            return false;
        }

    }

    public callSyncCreateEcontract (infoReq: any) : Promise<any> {
        return new Promise(async (resolvePr) => {

            if (!infoReq || !infoReq.body || !infoReq.body.hasOwnProperty('fosNo')|| !infoReq.body.hasOwnProperty('cusNo')) {
                return resolvePr({ 'result': false, messageErr: 'Call error param fosNo  ,cusNo not valid' });
            }

            let { fosNo, cusNo } = infoReq.body;

            const agent = new https.Agent({
                rejectUnauthorized: false,
                keepAlive: true
            });

            let data = JSON.stringify({
                "fosNo": fosNo,
                "cusNo": cusNo
            });

            let configCall = {
                method: 'post',
                maxBodyLength: Infinity,
                url: urlCreateEcontract,
                headers: {
                    'Content-Type': 'application/json'
                },
                data : data
            };

            // @ts-ignore
            await axios.request(configCall)
                .then((response) => {
                    logger.info(JSON.stringify(response.data));
                    let {success, message} = response.data;
                    success = success && _.isBoolean(success) ? success :false;
                    return resolvePr({ 'result': success, messageErr: message });
                })
                .catch((error) => {
                    logger.info(error);
                    return resolvePr({ 'result': false, messageErr: JSON.stringify(error) });
                });



        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        });;
    }
}

const insCopyImageService = new CopyImageService()
export default insCopyImageService
