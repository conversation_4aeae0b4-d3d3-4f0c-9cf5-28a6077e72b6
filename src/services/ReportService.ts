const fs = require('fs');
const _ = require('lodash');
import axios from 'axios';
import logger from '../config/logger';
// NhacVB R3456
const PATH_REPORT = process.env.PATH_REPORT || 'reports'
const FOS_REPORT_URL = process.env.FOS_REPORT_URL || ''
const FOS_REPORT_KEY = process.env.FOS_REPORT_KEY || ''

// const PATH_JSON_TO_FILE = process.env.PATH_JSON_TO_FILE || 'json2file'

class ReportService {
    public readFileStream = (filename : any): Promise<any> =>{
        return new Promise(async (resolve) => {  
            try {
                logger.info('filename: ' + filename);
                // let typeFile = ".txt"
                const pathreport = PATH_REPORT  + '/'+ filename
                logger.info('pathreport: ' + pathreport);

                if(fs.existsSync(pathreport)){
                    fs.readFile( pathreport, function (err:any, data:any) {
                        if(err){
                            resolve(err)
                        }
                        try{
                            logger.info(JSON.parse(data));
                            // fs.unlink(pathreport,function(errUnlink:any){
                            //     if(errUnlink) return logger.info("errUnlink: "+ JSON.stringify(errUnlink));
                            //     logger.info(`file ${filename} deleted successfully`);
                            // });  
                            return resolve(  {
                                message: "Lấy thông tin thành công",
                                succes: true,
                                data: JSON.parse(data),
                                clientCode: "200"
                            });
                        }
                        catch(error){
                            return resolve(  {
                                message: "Data invalid from file",
                                succes: false,
                                data: error,
                                clientCode: "400"
                            });
                        }      
                    });
                }else{
                    resolve(  {
                        message: "Tên file không tồn tại",
                        succes: false,
                        data: null,
                        clientCode: "400"
                    });
                }
            }catch (error){
                resolve({ codeALT: '209', messageErr: JSON.stringify(error) });
            }
        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        });
    }
    public json2file = (input:any): Promise <any>=>{
        const {data , filename} = input
        logger.info(input);
        return new Promise((resolve, reject) => {
            try {
                // kiểm tra data 
                // kiểm tra tên file
                //  ghi đè file
                const pathreport = PATH_REPORT  + '/'+ filename
                fs.writeFile(pathreport, JSON.stringify(data), function(err:any) {
                    if(err) {
                        console.log('err: ', err);
                      return resolve("400") 
                    }
                    return resolve("200") 
                }); 
            } catch(error){
                console.log('error: ', error);
                return reject({ codeALT: '209', messageErr: error })
            }
        });
    }

    public getFOSReport = async (input: any): Promise<any> => {
        try {
            var result = await axios.post('api/report', input, {
                baseURL: FOS_REPORT_URL,
                headers: {
                    'report-api-key': FOS_REPORT_KEY,
                    'Content-Type': 'application/json',
                },
            });
            return result.data;
        } catch (error) {
            return {
                message: 'Process fail',
                success: false,
            };
        }
    };
}

const insReportService = new ReportService();
export default insReportService;
