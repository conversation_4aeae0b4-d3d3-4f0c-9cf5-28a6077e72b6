import logger from '../config/logger';
import axios from 'axios';
import moment from 'moment';
import crypto  from 'crypto';


const fs = require('fs');
const _ = require('lodash');

const urlDownImage = process.env.URL_SOL_IMG;
const urlUpdateStatus = process.env.URL_SOL_STATUS;

const dir_upload_img = process.env.DIR_UPLOAD_IMG || 'temp-upload';

console.log('urlDownImage ' + urlDownImage);

const checkImageFileName = (fileName: string): RegExpMatchArray => {
    logger.info('fileName ' + fileName);
    fileName = fileName ? fileName.toLowerCase() : '';
    let boolCheck = fileName.match(/\.(jpg|jpeg|png|gif)$/i);
    logger.info('fileName ' + fileName + ' is ' + boolCheck);
    return boolCheck;
};

const base64ToImg = async (base64String: string, filename: string): Promise<boolean> => {
    try {
        let flag = false;

        logger.info('filename >> ' + filename + ' to file begin');

        if (_.isNil(filename) || filename == '') {

            logger.info('filename >> ' + filename + ' >>> not valid ' + flag + ' >> to file end');
            return false;
        }

        const fileDataDecoded = Buffer.from(base64String, 'base64');

        await new Promise((resolve, reject) => {
            fs.writeFile(dir_upload_img + '/' + filename, fileDataDecoded, function(err: any) {
                if (!err) {
                    flag = true;
                } else {
                    logger.info('filename >> ' + filename + ' >>> error ' + err);
                }
                resolve(flag);
            });
        });

        logger.info('filename >> ' + filename + ' >>> save success ' + flag + ' >> to file end');

        return flag;

    } catch (error) {
        logger.info('base64ToImg error: ' + error);
        return false;
    }
};

class BankService {
    public getImage = (infoReq: any): Promise<any> => {
        const seft = this;

        return new Promise(async (resolve) => {
            try {

                let idCard = infoReq.body.idCard;

                let flagUpload = true;
                let messageErr = '';

                if (!infoReq || !infoReq.body || !infoReq.body.hasOwnProperty('idCard')) {
                    return resolve({ 'upload': false, messageErr: 'Call error param idCard not valid' });
                }

                logger.info('idCard is ' + idCard);

                let { fileName1, fileName2, fileName3 } = infoReq.body;

                // if (!checkImageFileName(fileName1) || !checkImageFileName(fileName2)) {
                //     return resolve({
                //         'upload': false,
                //         messageErr: 'Call error param fileName 1 or fileName 2 not valid',
                //     });
                //
                // }

                const refno = idCard + moment().format('YYYYMMDDhhmmss');

                const dataCall = {
                    'scrt_org_c': 'SSVN1',
                    'refno': refno,
                    'data': {
                        'idCard': idCard,
                    },
                };

                logger.info('Json call is ' + JSON.stringify(dataCall));

                const privateKey = fs.readFileSync("key/private.pem", "utf8");

                const data = Buffer.from(JSON.stringify(dataCall));

                const signature = crypto.sign('RSA-SHA256', data, privateKey).toString("base64");
                logger.info(`Signing done ${signature}`);


                const configCall = {
                    method: 'post',
                    url: urlDownImage,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Basic ' + process.env.SOL_AUTH,
                        'signature': signature,
                    },
                    data: dataCall,
                };

                let fullNameSOl = '';
                let domorAdressSOl = '';
                let permAdressSOl = '';
                let issuePlaceSOl = '';

                // @ts-ignore
                axios(configCall)
                    .then(async function(response: any) {
                        let responseData = response.data;
                        logger.info('JSON response >> ' + JSON.stringify(responseData) + '....<<');
                        logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 100) + '....<<');

                        if (responseData && responseData.hasOwnProperty('data')) {
                            let {
                                base64ImgFront,
                                base64ImgBack,
                                base64Selfie,
                                userName,
                                domorAdr,
                                permAdr,
                                issuePlace,
                            } = responseData.data;

                            fullNameSOl = userName;
                            domorAdressSOl = domorAdr;
                            permAdressSOl = permAdr;
                            issuePlaceSOl = issuePlace;

                            if (base64ImgFront && base64ImgBack) {

                                logger.info('base64ImgFront >> ' + base64ImgFront.substring(0, 3) + '....<< length ' + base64ImgFront.length);
                                logger.info('base64ImgBack >> ' + base64ImgBack.substring(0, 3) + '....<< length ' + base64ImgBack.length);
                                logger.info('base64Selfie >> ' + base64Selfie.substring(0, 3) + '....<< length ' + base64Selfie.length);

                                await new Promise(async (resolve1, reject) => {
                                    fs.access(dir_upload_img, async (error: any) => {
                                        // To check if the given directory
                                        // already exists or not
                                        if (error) {
                                            resolve({
                                                'upload': false,
                                                'messageErr': 'Directory store image not exits',
                                                'fullNameSOl': fullNameSOl,
                                                'domorAdressSOl': domorAdressSOl,
                                                'permAdressSOl': permAdressSOl,
                                                'issuePlaceSOl': issuePlaceSOl,
                                            });
                                        } else {
                                            let flagImg1 = await base64ToImg(base64ImgFront, fileName1);
                                            let flagImg2 = await base64ToImg(base64ImgBack, fileName2);
                                            let flagImg3 = await base64ToImg(base64Selfie, fileName3);

                                            logger.info('base64ImgFront upload ' + flagImg1);
                                            logger.info('base64ImgFront upload ' + flagImg2);
                                            logger.info('base64Selfie upload ' + flagImg3);

                                            if (!flagImg1 || !flagImg2 || !flagImg3) {
                                                flagUpload = false;
                                                messageErr = 'Image write fail.Check Api or Permission write file.';
                                            }

                                        }
                                        resolve1('');
                                    });
                                });
                            } else {
                                resolve({
                                    'upload': false,
                                    'messageErr': 'Vendor call fail base64ImgFront & base64ImgBack',
                                    'fullNameSOl': '',
                                    'domorAdressSOl': '',
                                    'permAdressSOl': '',
                                    'issuePlaceSOl': '',
                                });
                            }

                        } else {
                            resolve({
                                'upload': false,
                                'messageErr': 'Vendor call fail',
                                'fullNameSOl': '',
                                'domorAdressSOl': '',
                                'permAdressSOl': '',
                                'issuePlaceSOl': '',
                            });
                        }

                        resolve({
                            'upload': flagUpload, 'messageErr': messageErr,
                            'fullNameSOl': fullNameSOl,
                            'domorAdressSOl': domorAdressSOl,
                            'permAdressSOl': permAdressSOl,
                            'issuePlaceSOl': issuePlaceSOl,
                        });
                    })
                    .catch(function(error) {
                        logger.info(error);
                        resolve({
                            'upload': false, 'messageErr': error,
                            'fullNameSOl': '',
                            'domorAdressSOl': '',
                            'permAdressSOl': '',
                            'issuePlaceSOl': '',

                        });
                    });

            } catch (error) {
                resolve({ codeALT: '209', messageErr: JSON.stringify(error) });
            }
        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        });
    };

    public updateStatus = (infoReq: any): Promise<any> => {
        const seft = this;

        return new Promise(async (resolve) => {
            try {


                let flagUpdate = true;
                let messageErr = '';

                if (!infoReq || !infoReq.body || !infoReq.body.hasOwnProperty('trxKey') || !infoReq.body.hasOwnProperty('acStatus') || !infoReq.body.hasOwnProperty('scrtAc')) {
                    return resolve({ 'updateAccStatus': false, messageErr: 'Call error param not valid' });
                }


                let { acStatus, trxKey, scrtAc } = infoReq.body;


                const dataCall = {
                    'scrt_org_c': 'SSVN1',
                    'data': {
                        'scrtAc': scrtAc,
                        'acStatus': acStatus,
                        'trxKey': trxKey,
                    },
                };

                logger.info('Json call is ' + JSON.stringify(dataCall));

                const privateKey = fs.readFileSync("key/private.pem", "utf8");

                const data = Buffer.from(JSON.stringify(dataCall));

                const signature = crypto.sign('RSA-SHA256', data, privateKey).toString("base64");
                logger.info(`Signing done ${signature}`);

                const configCall = {
                    method: 'post',
                    url: urlUpdateStatus,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Basic ' + process.env.SOL_AUTH,
                        'signature': signature,
                    },
                    data: dataCall,
                };


                // @ts-ignore
                axios(configCall)
                    .then(async function(response: any) {
                        let responseData = response.data;

                        logger.info('JSON response >> ' + JSON.stringify(responseData).substring(0, 200) + '....<<');

                        let { Code, Message } = responseData;

                        if (Code == '00') {
                            flagUpdate = true;
                            logger.info('trxKey ' + trxKey + '  >> update Code' + Code + '<<');
                        } else {
                            resolve({ 'updateAccStatus': false, 'messageErr': 'Vendor call fail' });
                        }

                        resolve({ 'updateAccStatus': flagUpdate, 'messageErr': messageErr });
                    })
                    .catch(function(error) {
                        logger.info(error);
                        resolve({ 'updateAccStatus': false, 'messageErr': error });
                    });

            } catch (error) {
                resolve({ codeALT: '209', messageErr: JSON.stringify(error) });
            }
        }).catch(error => {
            logger.info(error);
            return { codeALT: '209', messageErr: JSON.stringify(error) };
        });
    };
}

const insBankService = new BankService();
export default insBankService;