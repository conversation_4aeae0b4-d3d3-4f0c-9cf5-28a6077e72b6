import IRequest from 'IRequest';
import multiparty from 'multiparty';
import sharp from 'sharp';
import process from 'process';
import path from 'path';
import fs from 'fs';
import logger from '../config/logger';

const errorMessage = 'Invalid request.'

const validateRequest = (fields: any, files: { file: any[] }) => {
  console.log({ fields, files })
  if (!fields.imageName || !fields.imageName[0].trim()) {
	throw new Error(errorMessage);
  }

  const file = files.file?.[0];
  if (
	  !file?.path ||
	  files.file.length !== 1 ||
	  !file.headers['content-type']
  ) {
	throw new Error(errorMessage);
  }
}

const validateRequestVideo = (fields: any, files: { file: any[] }) => {
  if (!fields.videoName || !fields.videoName[0].trim()) {
	throw new Error(errorMessage);
  }

  const file = files.file?.[0];
  if (
	  !file?.path ||
	  files.file.length !== 1 ||
	  !file.headers['content-type']
  ) {
	throw new Error(errorMessage);
  }
}

class UploadImageCallService {
  public uploadImage = async (req: IRequest): Promise<any> => {
	const form = new multiparty.Form();

	return await new Promise((resolve, reject) => {
			// tslint:disable-next-line:ter-prefer-arrow-callback
	  form.parse(req, async function (err, fields, files) {
		try {
		  if (err || !files || !fields) {
			reject(new Error(errorMessage));
			return;
		  }

		  validateRequest(fields, files);

		  const file = files.file[0];
		  const fileName = fields.imageName;
		  const channel = fields.channel;

		  const imageBuffer = await sharp(file.path)
						.toFormat('png')
						.toBuffer();
		  const folderPathVideoCall = process.env.DIR_UPLOAD_IMAGE_VIDEO_CALL || 'temp-upload';
		  const folderPath = path.resolve(folderPathVideoCall);
		  const newPath = path.resolve(`${folderPath}/${fileName}`)

		  if (!fs.existsSync(folderPath)) {
			console.log('Folder không tồn tại')
			reject(new Error(errorMessage));
			return;
		  }

		  if (fs.existsSync(newPath) && !channel) {
			console.log('File đã tồn tại');
			reject(new Error('File name already exists'));
			return;
		  }

		  await fs.promises.writeFile(newPath, imageBuffer);

		  const imageBase64 = imageBuffer.toString('base64');

		  resolve({
			data: `data:image/png;base64,${imageBase64}`,
		  });
		} catch (error) {
		  console.log('Lỗi rồi: ', error)
		  reject(error);
		  return;
		}
	  });
	});
  }
  public downloadImage = async (filename: any[]): Promise<any> => {
	const output: any = {
	  gttt_front: null,
	  gttt_back: null,
	  portrait: null,
	} 
	const folderPathVideoCall = process.env.DIR_UPLOAD_IMAGE_VIDEO_CALL || 'temp-upload';
	const folderPath = path.resolve(folderPathVideoCall);
	logger.info( `INFO: downloadImage`, folderPath );
	for(let i = 0; i < filename.length; i++){
	  const pathImage = path.resolve(`${folderPath}/${filename[i]}`)
	  try {
		if (fs.existsSync(folderPath)) {
		  const imageBase64 = fs.readFileSync(pathImage, { encoding: 'base64'})
		  output.gttt_front = filename[i].includes('gttt_front') ? imageBase64 :  output.gttt_front
		  output.gttt_back  = filename[i].includes('gttt_back') ? imageBase64 :  output.gttt_back
		  output.portrait   = filename[i].includes('portrait') ? imageBase64 :  output.portrait
		} else {
		  // Không có tồn tại Path
		  logger.error( `ERROR: no exist pathImage ${pathImage}`);
		}
	  } catch (error) {
		logger.error( `ERROR: pathImage ${pathImage}`, error);
	  }
	}
	return output
  }

  public uploadVideo = async (req: IRequest): Promise<any> => {
	const form = new multiparty.Form();
	return await new Promise((resolve, reject) => {
			// tslint:disable-next-line:ter-prefer-arrow-callback
	  form.parse(req, async function (err, fields, files) {
		try {
		  if (err || !files || !fields) {
			reject(new Error(errorMessage));
			return;
		  }

		  validateRequestVideo(fields, files);

		  const file = files.file[0];
		  const fileName = fields.videoName;
		  const channel = fields.channel;
		   
		  const folderPathVideoCall = process.env.DIR_UPLOAD_IMAGE_VIDEO_CALL || 'temp-upload';
		  const folderPath = path.resolve(folderPathVideoCall);
		  const newPath = path.resolve(`${folderPath}/${fileName}`) 

		  if (!fs.existsSync(folderPath)) {
			console.log('Folder không tồn tại')
			reject(new Error(errorMessage));
			return;
		  }

		  if (fs.existsSync(newPath)  && !channel) {
			console.log('File đã tồn tại');
			reject(new Error('File name already exists'));
			return;
		  }

		  const imageBuffer : any = await new Promise((resolve,reject)=>{
			fs.readFile(file.path, function(err, buffer){
			  if(err) reject(err)
			  resolve(buffer)
			})
		  })
		  await fs.promises.writeFile(newPath, imageBuffer);

		  // thuc luu
		  // await fs.rename(file.path, newPath, function (err) {
		  //   if (err) throw err;
		  //   resolve({})
		  // });
		  resolve({success: true, message: 'Successed'})
		} catch (error) {
		  console.log('API Lỗi uploadVideo rồi: ', error)
		  reject(error);
		  return;
		}
	  });
	});
  }
}

const insUploadImageCallService = new UploadImageCallService()
export default insUploadImageCallService