import express from 'express';
import UploadAvatarController from '../../controllers/upload-avatar.controller';
import { rateLimiter } from '../../middlewares/rateLimiter';

const router = express.Router();

router.post(
    '/',
    rateLimiter,
    UploadAvatarController.uploadAvatar,
);

router.get(
    '/',
    rateLimiter,
    UploadAvatarController.getAvatar,
)

router.post(
    '/generate',
    rateLimiter,
    UploadAvatarController.uploadGenerateAvatar,
);

export default router;
