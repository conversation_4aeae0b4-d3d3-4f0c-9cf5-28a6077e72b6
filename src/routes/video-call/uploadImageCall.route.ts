import express from 'express';
import uploadImageCallController from '../../controllers/upload-image-call.controller';
import { rateLimiter } from '../../middlewares/rateLimiter';
const router = express.Router();

router.post(
    '/image',
    rateLimiter,
    uploadImageCallController.uploadImage,
);

router.post(
    '/download-image',
    rateLimiter,
    uploadImageCallController.downloadImage,
);

router.post(
    '/video',
    rateLimiter,
    uploadImageCallController.uploadVideo,
);
export default router;