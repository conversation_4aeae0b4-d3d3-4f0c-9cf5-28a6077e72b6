import express from 'express';
import demoController from '../../controllers/demo.controller';

const router = express.Router();

// User CRUD operations
router.post('/users', demoController.createUser);
router.get('/users', demoController.getUsers);
router.get('/users/stats', demoController.getUserStats);
router.get('/users/:id', demoController.getUserById);
router.put('/users/:id', demoController.updateUser);
router.delete('/users/:id', demoController.deleteUser);

// Bulk operations
router.post('/users/bulk', demoController.createMultipleUsers);

export default router;
