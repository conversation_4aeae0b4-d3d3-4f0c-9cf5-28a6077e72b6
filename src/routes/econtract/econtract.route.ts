import express from 'express';
import { celebrate } from 'celebrate';
import ekycSchema from '../../constants/schema/ekyc.schema';
import econtractController from '../../controllers/econtract.controller';
const router = express.Router();

router.post(
    '/getSession',
    econtractController.callGetSession
);

router.post(
    '/getNewCookie',
    econtractController.callGetNewCookie
);

router.post(
    '/getAllTemplate',
    econtractController.callGetAllTemplate
);

router.post(
    '/callExtendDateExpire',
    econtractController.callExtendDateExpire
);

router.post(
    '/callCheckKeyLinkExit',
    econtractController.callCheckKeyLinkExit
);




export default router;