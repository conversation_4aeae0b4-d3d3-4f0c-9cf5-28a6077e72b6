import express from 'express';
import ekycImageController from '../../controllers/ekyc-image.controller';
import IRequest from '../../types/IRequest';
import { expressjwt } from 'express-jwt';

const router = express.Router();

const generateTokenExample = {
    header: {
        alg: 'HS256',
        typ: 'JWT',
    },
    payload: { seccode: '888', name: 'ALTISSS', permissions: 'admin' },
    secret: 'secret_by_888',
};

interface IToken {
    header: {
        alg: string | 'HS256';
        typ: string | 'JWT';
    };
    payload: {
        seccode: '888' | '020' | '061' | '081' | '099' | '020';
        name: 'ALTISSS';
        permissions:
            | 'admin'
            | 'supervisor'
            | 'viewonly'
            | 'maker'
            | 'checker';
        env: 'UAT';
    };
    signature: string;
}

const getSecret = async function (req: IRequest, token: IToken) {
    const { seccode, name, permissions } = token.payload;
    // console.log("OAuth2 token", token, req);

    if (permissions === 'admin' && req.query.seccode === seccode) {
        return `${process.env.SECRET_KEY_DOWNLOAD}_${seccode}`;
    }

    throw new Error('secret_notmatch');
};

router.get('/getImageEkyc',
    // expressjwt({ secret: getSecret, algorithms: ['HS256'] }),
    // celebrate(ekycSchema.addFaceToFPT),
    ekycImageController.getImageEkyc,
)

export default router;
