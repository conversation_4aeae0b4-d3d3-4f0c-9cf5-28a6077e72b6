import httpStatusCodes from 'http-status-codes';
import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import databaseConnectionService from '../services/DatabaseConnectionService';
import logger from '../config/logger';

// Health check endpoint
const healthCheck: IController = async (_req, res) => {
    try {
        const startTime = Date.now();
        
        // Check database connection
        const dbHealth = await databaseConnectionService.healthCheck();
        
        // Check memory usage
        const memoryUsage = process.memoryUsage();
        
        // Check uptime
        const uptime = process.uptime();
        
        // Calculate response time
        const responseTime = Date.now() - startTime;
        
        const healthData = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: {
                seconds: Math.floor(uptime),
                human: formatUptime(uptime)
            },
            database: dbHealth,
            memory: {
                rss: formatBytes(memoryUsage.rss),
                heapTotal: formatBytes(memoryUsage.heapTotal),
                heapUsed: formatBytes(memoryUsage.heapUsed),
                external: formatBytes(memoryUsage.external),
                usage: {
                    rss: memoryUsage.rss,
                    heapTotal: memoryUsage.heapTotal,
                    heapUsed: memoryUsage.heapUsed,
                    external: memoryUsage.external
                }
            },
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                env: process.env.NODE_ENV || process.env.ENV || 'development'
            },
            responseTime: `${responseTime}ms`
        };

        // Determine overall health status
        let overallStatus = 'healthy';
        let statusCode = httpStatusCodes.OK;

        if (dbHealth.status === 'disconnected' || dbHealth.status === 'error') {
            overallStatus = 'unhealthy';
            statusCode = httpStatusCodes.SERVICE_UNAVAILABLE;
        }

        healthData.status = overallStatus;

        logger.info(`Health check completed: ${overallStatus} (${responseTime}ms)`);
        
        apiResponse.result(res, healthData, statusCode);
    } catch (error) {
        logger.error('Health check failed:', error);
        
        const errorData = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
            uptime: {
                seconds: Math.floor(process.uptime()),
                human: formatUptime(process.uptime())
            }
        };

        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Health check failed'
        );
    }
};

// Detailed health check with more information
const detailedHealthCheck: IController = async (_req, res) => {
    try {
        const startTime = Date.now();
        
        // Basic health info
        const basicHealth = await getBasicHealthInfo();
        
        // Database statistics
        let dbStats = null;
        try {
            if (databaseConnectionService.getConnectionStatus()) {
                dbStats = await databaseConnectionService.getDatabaseStats();
            }
        } catch (error) {
            logger.warn('Could not get database stats:', error);
        }
        
        // Collections info
        let collections: string[] = [];
        try {
            if (databaseConnectionService.getConnectionStatus()) {
                collections = await databaseConnectionService.listCollections();
            }
        } catch (error) {
            logger.warn('Could not list collections:', error);
        }
        
        // System information
        const systemInfo = getSystemInfo();
        
        const responseTime = Date.now() - startTime;
        
        const detailedData = {
            ...basicHealth,
            database: {
                ...basicHealth.database,
                statistics: dbStats,
                collections: collections
            },
            system: systemInfo,
            responseTime: `${responseTime}ms`
        };

        logger.info(`Detailed health check completed (${responseTime}ms)`);
        
        apiResponse.result(res, detailedData, httpStatusCodes.OK);
    } catch (error) {
        logger.error('Detailed health check failed:', error);
        
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Detailed health check failed'
        );
    }
};

// Readiness probe (for Kubernetes)
const readinessProbe: IController = async (_req, res) => {
    try {
        const isReady = databaseConnectionService.getConnectionStatus();
        
        if (isReady) {
            res.status(httpStatusCodes.OK).json({ status: 'ready' });
        } else {
            res.status(httpStatusCodes.SERVICE_UNAVAILABLE).json({ status: 'not ready' });
        }
    } catch (error) {
        logger.error('Readiness probe failed:', error);
        res.status(httpStatusCodes.SERVICE_UNAVAILABLE).json({ 
            status: 'not ready',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// Liveness probe (for Kubernetes)
const livenessProbe: IController = async (_req, res) => {
    try {
        // Simple check - if we can respond, we're alive
        res.status(httpStatusCodes.OK).json({ 
            status: 'alive',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Liveness probe failed:', error);
        res.status(httpStatusCodes.INTERNAL_SERVER_ERROR).json({ 
            status: 'dead',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// Helper functions
async function getBasicHealthInfo() {
    const dbHealth = await databaseConnectionService.healthCheck();
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    return {
        status: dbHealth.status === 'connected' ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: {
            seconds: Math.floor(uptime),
            human: formatUptime(uptime)
        },
        database: dbHealth,
        memory: {
            rss: formatBytes(memoryUsage.rss),
            heapTotal: formatBytes(memoryUsage.heapTotal),
            heapUsed: formatBytes(memoryUsage.heapUsed),
            external: formatBytes(memoryUsage.external),
            usage: memoryUsage
        }
    };
}

function getSystemInfo() {
    return {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuUsage: process.cpuUsage(),
        environment: process.env.NODE_ENV || process.env.ENV || 'development',
        pid: process.pid,
        ppid: process.ppid,
        title: process.title
    };
}

function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (secs > 0) parts.push(`${secs}s`);
    
    return parts.join(' ') || '0s';
}

export default {
    healthCheck,
    detailedHealthCheck,
    readinessProbe,
    livenessProbe
};
