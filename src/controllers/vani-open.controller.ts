import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';
import vaniOpenApiService from '../services/vaniOpenServices'
// Tạo Key API 

const hashSHA256 = process.env.VANI_HEADER || "25d6a9f6620134608c2357d14cae3e27cea537f784765746b72d7674a6d9c561" //10VaniAlpha2023

const vaniPostback : IController = async (req, res) =>{   
    // thực thi gọi API  
    try {
        const { body, headers }= req;
        logger.info(JSON.stringify(body));
        logger.info("-----------------Start PostBack Vani-----------------------");
        logger.info('body, headers: ', body, headers);
        if( !headers['x-vani-x'] || headers['x-vani-x'] && headers['x-vani-x'] != hashSHA256){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
            ); 
            return   
        } 
        const result:any = await vaniOpenApiService.vaniPostback(body)
        logger.info(JSON.stringify(result));
        apiResponse.result(res, { 
            responseServer: 'ok',
            response: {
                body: body,
                clientCode: result
            }            
         });
        logger.info("-----------------End PostBack Vani-----------------------");
    } catch (e) {
        logger.info("----------------Error End PostBack Vani-----------------------");
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
} 

export default {
    vaniPostback
}   
