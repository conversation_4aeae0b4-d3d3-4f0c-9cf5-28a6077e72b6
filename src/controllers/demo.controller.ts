import httpStatusCodes from 'http-status-codes';
import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import databaseConnectionService from '../services/DatabaseConnectionService';
import logger from '../config/logger';

/**
 * Demo Controller - <PERSON> h<PERSON> cách sử dụng MongoDB Service
 * Các endpoints này demo các operations CRUD cơ bản
 */

// Demo collection name
const DEMO_COLLECTION = 'demo_users';

// Interface cho demo user
interface DemoUser {
    _id?: any;
    name: string;
    email: string;
    age?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

// CREATE - Tạo user mới
const createUser: IController = async (req, res) => {
    try {
        const { name, email, age } = req.body;
        
        if (!name || !email) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'Name and email are required'
            );
        }

        // <PERSON><PERSON><PERSON> tra email đã tồn tại chưa
        const existingUser = await databaseConnectionService.findOne<DemoUser>(
            DEMO_COLLECTION,
            { email }
        );

        if (existingUser) {
            return apiResponse.error(
                res,
                httpStatusCodes.CONFLICT,
                'Email already exists'
            );
        }

        // Tạo user mới
        const newUser: DemoUser = {
            name,
            email,
            age: age || null,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const result = await databaseConnectionService.insert<DemoUser>(
            DEMO_COLLECTION,
            newUser
        );

        logger.info(`User created with ID: ${result.insertedId}`);

        apiResponse.result(res, {
            message: 'User created successfully',
            userId: result.insertedId,
            user: { ...newUser, _id: result.insertedId }
        }, httpStatusCodes.CREATED);

    } catch (error) {
        logger.error('Error creating user:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to create user'
        );
    }
};

// READ - Lấy danh sách users với pagination
const getUsers: IController = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, minAge, maxAge } = req.query;
        
        // Build filter
        const filter: any = {};
        
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ];
        }
        
        if (minAge || maxAge) {
            filter.age = {};
            if (minAge) filter.age.$gte = parseInt(minAge as string);
            if (maxAge) filter.age.$lte = parseInt(maxAge as string);
        }

        // Get paginated results
        const result = await databaseConnectionService.findWithPagination<DemoUser>(
            DEMO_COLLECTION,
            filter,
            parseInt(page as string),
            parseInt(limit as string),
            { createdAt: -1 } // Sort by newest first
        );

        logger.info(`Retrieved ${result.data.length} users (page ${page})`);

        apiResponse.result(res, {
            users: result.data,
            pagination: result.pagination,
            filters: { search, minAge, maxAge }
        }, httpStatusCodes.OK);

    } catch (error) {
        logger.error('Error getting users:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to get users'
        );
    }
};

// READ - Lấy user theo ID
const getUserById: IController = async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'User ID is required'
            );
        }

        const user = await databaseConnectionService.findOne<DemoUser>(
            DEMO_COLLECTION,
            { _id: id }
        );

        if (!user) {
            return apiResponse.error(
                res,
                httpStatusCodes.NOT_FOUND,
                'User not found'
            );
        }

        logger.info(`Retrieved user: ${id}`);

        apiResponse.result(res, {
            user
        }, httpStatusCodes.OK);

    } catch (error) {
        logger.error('Error getting user by ID:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to get user'
        );
    }
};

// UPDATE - Cập nhật user
const updateUser: IController = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, email, age } = req.body;
        
        if (!id) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'User ID is required'
            );
        }

        // Build update object
        const updateData: any = {
            updatedAt: new Date()
        };
        
        if (name) updateData.name = name;
        if (email) updateData.email = email;
        if (age !== undefined) updateData.age = age;

        // Check if email is being changed and already exists
        if (email) {
            const existingUser = await databaseConnectionService.findOne<DemoUser>(
                DEMO_COLLECTION,
                { email, _id: { $ne: id } }
            );

            if (existingUser) {
                return apiResponse.error(
                    res,
                    httpStatusCodes.CONFLICT,
                    'Email already exists'
                );
            }
        }

        const result = await databaseConnectionService.updateOne<DemoUser>(
            DEMO_COLLECTION,
            { _id: id },
            { $set: updateData }
        );

        if (result.matchedCount === 0) {
            return apiResponse.error(
                res,
                httpStatusCodes.NOT_FOUND,
                'User not found'
            );
        }

        logger.info(`User updated: ${id}`);

        // Get updated user
        const updatedUser = await databaseConnectionService.findOne<DemoUser>(
            DEMO_COLLECTION,
            { _id: id }
        );

        apiResponse.result(res, {
            message: 'User updated successfully',
            user: updatedUser
        }, httpStatusCodes.OK);

    } catch (error) {
        logger.error('Error updating user:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to update user'
        );
    }
};

// DELETE - Xóa user
const deleteUser: IController = async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'User ID is required'
            );
        }

        const result = await databaseConnectionService.deleteOne(
            DEMO_COLLECTION,
            { _id: id }
        );

        if (result.deletedCount === 0) {
            return apiResponse.error(
                res,
                httpStatusCodes.NOT_FOUND,
                'User not found'
            );
        }

        logger.info(`User deleted: ${id}`);

        apiResponse.result(res, {
            message: 'User deleted successfully',
            deletedId: id
        }, httpStatusCodes.OK);

    } catch (error) {
        logger.error('Error deleting user:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to delete user'
        );
    }
};

// BULK CREATE - Tạo nhiều users
const createMultipleUsers: IController = async (req, res) => {
    try {
        const { users } = req.body;
        
        if (!users || !Array.isArray(users) || users.length === 0) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'Users array is required'
            );
        }

        // Validate and prepare users
        const preparedUsers: DemoUser[] = users.map((user: any) => ({
            name: user.name,
            email: user.email,
            age: user.age || null,
            createdAt: new Date(),
            updatedAt: new Date()
        }));

        // Check for duplicate emails
        const emails = preparedUsers.map(u => u.email);
        const duplicateEmails = emails.filter((email, index) => emails.indexOf(email) !== index);
        
        if (duplicateEmails.length > 0) {
            return apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                'Duplicate emails found in request'
            );
        }

        const result = await databaseConnectionService.insertMany<DemoUser>(
            DEMO_COLLECTION,
            preparedUsers
        );

        logger.info(`${result.insertedCount} users created`);

        apiResponse.result(res, {
            message: `${result.insertedCount} users created successfully`,
            insertedIds: result.insertedIds,
            count: result.insertedCount
        }, httpStatusCodes.CREATED);

    } catch (error) {
        logger.error('Error creating multiple users:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to create users'
        );
    }
};

// STATISTICS - Thống kê users
const getUserStats: IController = async (req, res) => {
    try {
        // Total count
        const totalUsers = await databaseConnectionService.count(DEMO_COLLECTION);
        
        // Age statistics using aggregation
        const ageStats = await databaseConnectionService.aggregate(DEMO_COLLECTION, [
            {
                $match: { age: { $ne: null } }
            },
            {
                $group: {
                    _id: null,
                    avgAge: { $avg: '$age' },
                    minAge: { $min: '$age' },
                    maxAge: { $max: '$age' },
                    count: { $sum: 1 }
                }
            }
        ]);

        // Email domains
        const emailDomains = await databaseConnectionService.aggregate(DEMO_COLLECTION, [
            {
                $project: {
                    domain: {
                        $arrayElemAt: [
                            { $split: ['$email', '@'] },
                            1
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: '$domain',
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { count: -1 }
            },
            {
                $limit: 10
            }
        ]);

        const stats = {
            totalUsers,
            ageStatistics: ageStats[0] || null,
            topEmailDomains: emailDomains,
            timestamp: new Date().toISOString()
        };

        logger.info('User statistics retrieved');

        apiResponse.result(res, stats, httpStatusCodes.OK);

    } catch (error) {
        logger.error('Error getting user stats:', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Failed to get user statistics'
        );
    }
};

export default {
    createUser,
    getUsers,
    getUserById,
    updateUser,
    deleteUser,
    createMultipleUsers,
    getUserStats
};
