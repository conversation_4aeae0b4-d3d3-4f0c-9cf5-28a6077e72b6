import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insForeignCustService from '../services/ForeignCustService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';

const hashSHA256 = process.env.DIR_HEADER_SHA256_ORDER || "0a0c6a03c9882f0d54b325f5b4ee170d8ef8fa1d0f598d6ba791750ac2b441fa" //shinhan@2022

const checkImageFileName = (fileName: string): RegExpMatchArray => {
    logger.info('fileName ' + fileName);
    fileName = fileName ? fileName.toLowerCase() : '';
    let boolCheck = fileName.match(/\.(jpg|jpeg|png|gif)$/i);
    logger.info('fileName ' + fileName + ' is ' + boolCheck);
    return boolCheck;
};


const uploadForeignCust: IController = async (req, res) => {
    try {
        const { body, headers }= req; 

        if( !headers['ssv-header-ca'] || headers['ssv-header-ca'] && headers['ssv-header-ca'] != hashSHA256){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
            return 
        }
        if(!checkImageFileName(body.filename.toString()) ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR
            );
            return
        }
        const result = await insForeignCustService.uploadToServer(body.filename, body.base64);
        logger.info(JSON.stringify(result));
        apiResponse.result(res, { responseServer: 'ok', response: result });
    } catch (e) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
} 
export default {
    uploadForeignCust
} 