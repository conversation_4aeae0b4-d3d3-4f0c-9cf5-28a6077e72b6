import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import insTradingViewService from '../services/TradingViewService';

const getSavedList: IController = async (req, res) => {
  try {
    const result = await insTradingViewService.getSavedList(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );

  }
  return apiResponse.result(res, { response: 'OK' });
}

const saveList: IController = async (req, res) => {
  try {
    const result = await insTradingViewService.saveList(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );

  }
  return apiResponse.result(res, { response: 'OK' });
}

const deleteChart: IController = async (req, res) => {
  try {
    const result = await insTradingViewService.deleteChart(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );

  }
  return apiResponse.result(res, { response: 'OK' });
}

export default {
  getSavedList,
  saveList,
  deleteChart
}
