import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insBankService from '../services/BankService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';

const callGetImage: IController = async (req, res) => {
    let {idCard} = req.body;

    if(!idCard) {
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );

    } else {
        try {
            const resImg = await insBankService.getImage(req);

            console.log(resImg);

            apiResponse.result(res, { response: resImg });
        } catch (e) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    }

}

const callUpdateStatusAcc: IController = async (req, res) => {
    let {acStatus, trxKey} = req.body;

    if(!acStatus || !trxKey) {
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );

    } else {
        try {
            const resUpdateAcc = await insBankService.updateStatus(req);

            console.log(resUpdateAcc);

            apiResponse.result(res, { response: resUpdateAcc });
        } catch (e) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    }

}

export default {
    callGetImage,
    callUpdateStatusAcc
}