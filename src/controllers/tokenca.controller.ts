import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insTokencaService from '../services/tokencaService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
const hashSHA256 = process.env.DIR_HEADER_SHA256_ORDER || "0a0c6a03c9882f0d54b325f5b4ee170d8ef8fa1d0f598d6ba791750ac2b441fa" //shinhan@2022

const uploadFileOrder: IController = async (req, res) => {
  try {
    const { body, headers }= req
    if( !headers['ssv-header-ca'] || headers['ssv-header-ca'] && headers['ssv-header-ca'] != hashSHA256){
      apiResponse.error(
        res,
        httpStatusCodes.FORBIDDEN,
      );
      return
    }
    if(!body.filename.toString().includes('.xml') ){
      apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.REQUEST_ERROR 
      );
      return
    }
    const result = insTokencaService.uploadToFTP(body.filename, body.base64)
    apiResponse.result(res, { session: 'ok', response: result });
  } catch (e) {
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
}
export default {
  uploadFileOrder
}