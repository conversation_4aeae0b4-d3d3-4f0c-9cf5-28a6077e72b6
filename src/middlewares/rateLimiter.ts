import { Request, Response, NextFunction } from 'express';
import moment from 'moment';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';

const rateLimitWindowMs = 60 * 1000; // 1 minute
const maxRequestsPerWindow = 60; // max number of requests per window per IP and endpoint

interface RequestCounts {
  [key: string]: {
    [key: string]: moment.Moment[];
  };
}

const requestCounts: RequestCounts = {};

const rateLimiter = (req: Request, res: Response, next: NextFunction): void => {
  const currentTime = moment();
  const clientIp = req.ip;
  const endpoint = req.originalUrl;

  if (!requestCounts[clientIp]) {
    requestCounts[clientIp] = {};
  }

  if (!requestCounts[clientIp][endpoint]) {
    requestCounts[clientIp][endpoint] = [];
  }

  const requests = requestCounts[clientIp][endpoint];

  requestCounts[clientIp][endpoint] = requests.filter(
      timestamp => currentTime.diff(timestamp) < rateLimitWindowMs,
  );

  if (requestCounts[clientIp][endpoint].length >= maxRequestsPerWindow) {
    apiResponse.error(res, httpStatusCodes.TOO_MANY_REQUESTS, 'Too many requests. Please try again later.');
  } else {
    requestCounts[clientIp][endpoint].push(currentTime);
    next();
  }
};

export
{
  rateLimiter,
};
