export type TImage ={
    base64: string   
    type: 'back' | 'front' | 'selfie' | 'qrvnid'   
    format: 'jpeg' | 'jpg' | 'png'
    fosid: string
}
export const CommonTypeImage : string[] = ['back', 'front', 'selfie', 'qrvnid']
export const CommonFormatImage : string[] = ['jpeg', 'jpg', 'png']

export interface ICardInfoReq {
    cccd?:  string, 
    dg1:    string,
    dg2:    string,
    dg3?:   string,
    dg4?:   string,
    dg5?:   string,
    dg6?:   string,
    dg7?:   string,
    dg8?:   string,
    dg9?:   string,
    dg10?:  string,
    dg11?:  string,
    dg12?:  string,
    dg13:   string,
    dg14?:  string,
    dg15?:  string,
    dg16?:  string,
    sod:    string,
    challenge?:     string,
    aAResult?:      string,
    eACCAResult?:   string,
}
export interface ICardInfoRes{
    code?: number
    success?: boolean
    message?: string
    data?: ICardDetail
    exitCode?: string
    exitCodeMessage?: string
    error?: any
    idCheck?: any
}
export interface ICardDetail {
    citizenPid: string,
    oldIdentify: string,
    fullName: string,
    birthDate: string,
    gender: string,
    ethnic: string,
    nationality: string,
    religion: string,
    dateProvide: string,
    outOfDate: string,
    homeTown: string,
    regPlaceAddress: string,
    identifyCharacteristics: string,
    photoBase64: string,
    motherName: string,
    fatherName: string,

    husBandName: string,
    wifeName: string,
    chipAuth: boolean,
    chipAuthMessage: string,
    cscaAuth: boolean,
    cscaAuthMessage: string,
    sodBase64:string ,
    hashCheck: boolean,
    husbandOrWifeName:string ,
    husBandOrWifeName: string,
    homeTownProvincesCode:string ,
    homeTownDistrictCode:string ,
    homeTownWardsCode: string,
    addressProvincesCode: string,
    addressDistrictCode: string,
    addressWardsCode: string,
    addressDetail: string,
    cloneChip: string,
    valid: boolean
}