declare global {

    type ISecCode = '888' | '081' | '028' | '061' | '036' | '020' | '004' | '102' | '023' | '075' | '082' | '099'
    type ICompanyName = 'altisss' | 'ssv' | 'nsi' | 'gtjai' | 'apsc' | 'vise' | 'ysvn' | 'vncsi' | 'vietsc' | 'beta' | 'hbse' | 'asam'
    type ICompanyNameUpper = 'ALTISSS' | 'SSV' | 'NSI' | 'GTJAI' | 'APSC' | 'VISE' | 'YSVN' | 'VNCSI' | 'VIETSC' | 'BETA' | 'HBSE' | 'ASAM'
    type ILanguage = 'vi' | 'en' | 'cn' | 'zh' | 'ko'
    type ILanguageUpper = 'VI' | 'EN' | 'CN' | 'KO' | 'ZH' | 'JA'

    enum ICurrentSupportCollection {
        UAT_888_ALTISSS = "UAT_888_ALTISSS",
        UAT_081_SSV = "UAT_081_SSV",
        UAT_036_APSC = "UAT_036_APSC",
        // ----- pro
        PRO_888_ALTISSS = "PRO_888_ALTISSS",
        PRO_036_APSC = "PRO_036_APSC",
        PRO_081_SSV = "PRO_081_SSV",

    }

    enum EnumSecCode {
        altisss = '888',
        shinhan = '081',
        nsi = '028',
        gtjai = '061',
        apsc = '036',
        vise = '020',
        ysvn = '004',
        vncsi = '102',
        vietsc = '023',
        beta = '075',
        hbse = '082',
        asam = '099',
    }

}
export { }
