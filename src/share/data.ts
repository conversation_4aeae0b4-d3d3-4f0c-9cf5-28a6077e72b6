export const CheckPC06:any = [
    {
        status: 200, 
        message:"",
        data: {
            detail: {
              id: '079094016000',
              'prev-number': '025183330',
              name: '<PERSON><PERSON> <PERSON><PERSON>ch <PERSON>hạxxxx',
              dob: '20/03/1994',
              sex: 'Nam',
              ethnic: 'Kinh',
              nationality: 'Việt Nam',
              religion: 'Phật giáo',
              'issue-date': '10/05/2021',
              'expired-date': '20/03/2034',
              home: 'Phường 4, Quận 8, TP.<PERSON><PERSON>',
              address: 'Phạm T<PERSON>ế Hiển,, Phường 4, Quận 8, TP.<PERSON><PERSON>',
              features: 'Quá đẹp trai',
              face: 'iiiiiiiiiiiiii',
              mother: '<PERSON><PERSON><PERSON><PERSON> xxxxx',
              father: '<PERSON><PERSON> xxxxxx',
              mrz: 'IDVNM0940160000079094016000<<09403202M3403200VNM<<<<<<<<<<<6VO<<BACH<NHAC<<<<<<<<<<<<<<<<<'
            },
            chip_check: {
              integrity_check: 'true',
              clone_check: 'true',
              authentication_check: 'true',
              rar_request_id: 'qts-b59d65d1-e62b-4e16-8937-a344b7ff1350'
            },
            code: '200',
            message: 'Authentication successful'
        }
    },
    {
        status: 200, 
        message:"",
        data: {
            "detail": {
                "id": "093097000055",
                "prev-number": "363901328",
                "name": "Lâm Chi Hàuuu",
                "dob": "15/07/1997",
                "sex": "Nam",
                "ethnic": "Kinh",
                "nationality": "Việt Nam",
                "religion": "Không",
                "issue-date": "13/03/2022",
                "expired-date": "15/07/2037",
                "home": "Vị Thắng, Vị Thủy, Hậu Giang",
                "address": "Vị Thắng, Vị Thủy, Hậu Giang",
                "features": "Quá đẹp trai",
                "face": "bchfdfhj",
                "mother": "Phạm xxxxxxx",
                "father": "Lâm xxxxxxxxx",
                "mrz": "IDVNM0970000554093097000055<<49707151M3707159VNM<<<<<<<<<<<2LAM<<CHI<HAO<<<<<<<<<<<<<<<<<<"
            },
            chip_check: {
                integrity_check: 'true',
                clone_check: 'true',
                authentication_check: 'true',
                rar_request_id: 'qts-b59d65d1-e62b-4e16-8937-a344b7ff1350'
            },
            code: '200',
            message: 'Authentication successful'
        },
    },
    {
        status: 200, 
        message:"",
        data: {
            "detail": {
                "id": "040201001534",
                "prev-number": "363901328",
                "name": "Đặng Hữuuuu Nham",
                "dob": "22/10/2001",
                "sex": "Nam",
                "ethnic": "Kinh",
                "nationality": "Việt Nam",
                "religion": "Không",
                "issue-date": "19/04/2021",
                "expired-date": "22/10/2026",
                "home": "Bình Dương",
                "address": "Bình Dương",
                "features": "Quá đẹp trai",
                "face": "bchfdfhj",
                "mother": "Phạm xxxxxxx",
                "father": "Lâm xxxxxxxxx",
                "mrz": "IDVNM0970000554093097000055<<49707151M3707159VNM<<<<<<<<<<<2DANG<<HUU<NHAM<<<<<<<<<<<<<<<<<<"
            },
            chip_check: {
                integrity_check: 'true',
                clone_check: 'true',
                authentication_check: 'true',
                rar_request_id: 'qts-b59d65d1-e62b-4e16-8937-a344b7ff1350'
            },
            code: '200',
            message: 'Authentication successful'
        },
    }

]

export const FakeReadNFC:any = [
    {
        status: 200,
        data: {
            "status": 200,
            "message": "Success",
            "exitCode": 0,
            "exitCodeMessage": "Success",
            "activityID": "m1426b-a38df1ed-7797-4cc7-bd81-6aa7a3471585-b1727r-u25778k",
            "data": {
                "birthDate": "20/03/1994",
                "citizenPid": "079094016000",
                "dateProvide": "10/05/2021",
                "ethnic": "Kinh",
                "fatherName": "Võ Văn Chiến",
                "fullName": "Võ Bách Nhạc",
                "gender": "Nam",
                "homeTown": "Phường 4, Quận 8, TP.Hồ Chí Minh",
                "husBandName": "",
                "identifyCharacteristics": "Nốt ruồi C:1cm trên sau cánh mũi phải",
                "motherName": "Nguyễn Thị Tiến",
                "nationality": "Việt Nam",
                "oldIdentify": "025183330",
                "outOfDate": "20/03/2034",
                "photoBase64": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAGQASwDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3XFLRRWJoFFFFABRRRQAUUUUAFFFFIAooopgFFFFIAppp1BoGNp1GKKAClFJS0CCiiimAUUUUAJRiiikAUtFFABRRRTAKKKKACk70tJQAUUUUgCiiigAooopgFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRS0UAHeiiigAoopKAFpKKKACiilpAFFFFMAooooAKKKKAEooooAKKKKQBRRRQAlFFFAC0UlFAC0UgpaACiiimAUUUUAFFFGaBhRTXljiBLuqgd2OKxr3xfoNjkTarahh2D7v5ZpXQWNummRFPLAfU155qXxc0y1YrZ27XOP4t+0fqK5XUvjBe3CstvawQ5PBJDf0ovcLHtgmiY4EiE+gYVJmvm2bxzq9wSDqIXPO1Qo/kKaPF2tjmPWLpWA4xMR+lGo7H0n+NFfNQ8eeJo3z/bd0T2G8H9CKt2/wAUPFkEqk6isyjqskC8/iAKNQsfRIOaXtXj+m/Gg7o4tV0soON9xDLn8duP613WlePPDuqKPK1SFWP8Mp8s/wDj2KVxWOlzS0xJEdco6sp6EHNPpgFLRRQIKKKSmAUZpaM0AJmiiikAUUUUAFFFFABRRRQAlJ3paKACiiigApaSgUALRRRQAUVHLKkKM8jBVUZJPavOPFXxXsNOiaHS1M85yu5lO0H8xSuM7rVdc0/RbYz31ykSds5JP0ArzDxF8YxHK0GjQrIMH964PX6V5hq+sX+t3b3V7Mcuc4ycfgKz1+7+7GfdulO19wOhvvGGuaq5NzfzbWP3fMOB+FY0txhz85kY9Saqby3BbPsOBSbtpwAAaqwyZmYA5YfiKiZXYcNn6LTkcBjjex/KneaRjEZx7mmIr4KnaF+b1zTtk3Xax9xUpuWBI2r+JzSLc7fmKLj2H+FADCCwxIxU+pGTTG3o37uRn/HFK9zC558wYHrmq7zxkYWQj6igCU3bn5WAJ9OlKs5J4LA+lV/tDAYba6+4zRvjYfxp7A8UNDuddonjnXdEcNbXrzIBgwzOzL+Wa9K8NfGOzumWDWohasT/AK5Msv4jGR9a8J3rjDH6MtPSQluT06Gp5QPsSC5iuYRLC6upGQVOQalU7lzXyz4d8X6v4bn82xnzFwHgckxnnP3QeD7j1Ne5eDviHpfiZYrbeYb8oCYWBAJ77T36UtgsdrRQDmigQUUUUxBRR3opAFFFFABRRRQAUUUUAJRRRQAUUUlAC0UmaM0ALVXUdRt9LspLu6kVIkHJJAqW4nS2geaQ4RBk187+OfG934jvpItxSxiYhEGOSCefejyQy34v+Ieo+IGktrZ1t7DPAxgt9TXCtPBCAeWk9TVSec9hyeg9KhUnGe/rVJBctPdl8/mophZmwZX+gFQoTztBz/ePenZKn0PvTsFyZWXOAvPrilWaJP8Abf26VVKTS9Txn060027rklwPYcmmK5prcSgcKgAoDq7kPj6jmsja7HAJx71IokX/AOvxQFzSaNQcrMpPocVHI8cZ+aNWH41UWR0+8+R/vcUxrhCPnLEDspNAE263kbkKp7ZJFJJbx+m4f7PNQiWGUY81k9nH9asrCUAYSIVz1HSgCsIUyev17UfZiT8rgHtg8VZmfpn8xyPzqozbTkce9AxhEiNjAxTd7A9MGrAkbbgruHrTWVCuD93sfSgLDUuGU9SDVqC42SrIjFHU5Vh2NUypXjAdfalHyjKZK+45FJoLns3gz4s3FuEsddbzk6R3AUAgAdCePzr2Gy1S1v41e3nSQN0AYE18ew3JTjP4V3PhjxZcWNt9mG4EY2P6Y7VDVhn0qCKdmuF0jxxaNCjXLcOBl9wwD6V2UNyk67kIK9AR3pXFYn7mlpo4FLmmIWiiigAopKKAClpKKAEpaSigBc0lFFABUNzdQ2du9xcSLHEgLMzHgCpScDNeNfFHxi00raLZSEKhxMykjd04pDKHjr4kTarcSWOmzPFYKSrMpx5vP0zivMrifzGIH4CnTyBExj5qqYPJ7nqfSrirAAVmbPU9z6VL5Sk/MeB2pYlzjg7B6HqaZI5kfCH5RVCHb1Bx/Kk3fNkLuz69qQJkEk++KkQhRnjb9KBijdj19zwKaYt3JOaGfJ9fal3qnJO9jxg9KBD0tlwTuxj+I1BLt5CDd+OaezyzYB4HoOKOAMAMx9AeKVx2KrK3Xr/IU3MhPQEe9WxFub97geiirHkRBOw/DJp3AynjJ5KgfTpUeGX7rEfQ1pTWy9ecns2BVZlUcccUXC1yuJJF43E+1OEm7hxgVIsTN0U49aVoTkDGaV0PlY0Epxuwp6GlJ2EEfiKTyiFPp6Um09Ov1ouOzDcpOU4Ppmgbgc/n71GRg+lOVjTEPMaOeGx/wGrEbeWNu8geuCKgXrk7fyqzFCj8yTMFpMRqW9+8MXlmTzMnI+cgZ5AP4da9a8Fajqcy2sCyXDZO6TP3EUMOQe/X9a8msbS1z5js79gP8jivTPBN7OZ5YoHxtTZs3AbcnoPy61DGexRNuUZYMak71maYlwLZTK3zY5XOSPxrRX3NIQ/NGaSimAE0ZoopCDNLmkooAKXtSUUwCg0UE4oGct478QLoHh6SQH99NmOMe5Br5svb15ZnnkO53Yn8TXoHxd1l7zXhYq37q3HQepArzBsu2SOOgFOK6jDzCzFmpyKXBJwB60jJlwMjgfN7UyaVRHgfh7VYiSaTgIhIGPxxSGQIMAYqqkhIyaGckc9TQIm8wtyWOOw9acoL/N1x69qjhidzkDj1rRt7J5D2C/maTkkXGLZBHEXOOQvrVlLZTg4A7VeitDvCRqc/54rTSwS2tzPJtAUHLE8fQe9ZOobRot6sxPs6R/fzz0A70wBSWBO0D+71/OpWL3crEqyR9eRyanismdQzKVj/AIR3anzaCcL6FEAuMQIFTu56mkSAM2UV3bp83St+z0iS7bCofLBx0611uneGY1ALp0rOVVI0hQvuefR6DdzLuCBc+9XrTwnKOZVHtzXp8elxRqNq082ak8DmsnVbN1SgjgV8PskOEABx1rN/4R2YysCgxnBPrXqH2MZ+7xSfYk7pU87LcYs8x/4R0jjyzg85FZtxozxM3yY9K9eayUfw1QutJSQEGPIPcVSqMTpxZ4/c2LRAEjg9KoFCpOB+len6h4fjfgKRiubufDrqZAqn2raNZHPUw76HKrvB4xU6SPjA2j3xzVm60iaBSWUiqCvJG2OMVspKRzTg47ltdQe34+836VtaP4sv9PdhtiIUYQEHjPU8H6Vzh+YF269BU+5WmJHQcU7EHqWieONSvJo4BdeRI5GzLsVb27163od7dTWy/bseZxgpyDxzXy/Z3RR9+SHU5BHrXr/w38TyX6ta3F3+9jwFR8ZYY/XpUNWGetg0tRRnIFSCgQUtFGKQgopcUYoASiiimAUxyFRieg5p1Q3JxbyHOPlNAz5g8WXbaj4hvZjncz/kAMf0rn3bbJuYghemO1bGsNv1O4K9Gc5x2FYcwyxVee5qo7AQtPhX45Y81XJ3HnqTT3Ug4ApoXHNUSG7jGO9WLe1kuJVRRyf0pLaHLZYZ9B6+1ddoekkDzHX5m4FZznyo2pU+ZlSx0t5p/JjX5Y8AnH3jWxdWZsIBCg/eSAEt/dGf8T/Oug0zTUh3ORjaSaqtCZNTEko3AszKD2UcAfnzXM5ts7400loR6dpiwx+bMuFx0PHFZV+ZNXv9ijFugAQD7p989xW1rVwxtlt4DvYkFgvAA9M/54p2naI6wG5ncn+6g7nPApJ9WU10MtdPXf8AZ4IvNk/iYg7Qe31rbs/D+2SNZyzzSDJIH3R/ngV0enaZDbRDCKCP4scmp7KFZLiW5wMyNwQSQVHA/Pr+NJybFZIZaWEMDJEFwMfKMdq01th9KsIq5BAAxUwwOtKxLZTMO0YphhrQKrjjnPpUZXpgUWC5VWLHWlaNT1FTMDn2ppx6VJRWePjiq5X15q5IwFQM2T0pDTM25t1ccis2Wx5JxxW44I7VA6ZNBVzldR05XiIC9a891TTzBKTt+lew3MG4cCuL17TsozAc59OlbU5WMaseZHnrZLc9hikU7C3qanuIdkzDpVcjKHnmuxannNWLMTlWPr1rU06eVZ1khLrIpyCp5HvWTE26MZ6irlpcGGcMp5HX3oYI+kvBWsy6lpcQuDmVVG5s9eBz7V1oNcF4KSIWsU9o++FlGB/d4HHt9K7tTkZyazAfS00GlpiFpaSigBKKKTPNACHrVLU5fJ064c9BGx/Srh61yXj/AFlNI8N3JBBmlUooPOMg0mM+eb2QfaZT6k1VEQKZGNz/AKUjv+/CseTzTvM649eK0QFWWIZYKMKvGfWo3QKuBzzVuTAKr2PzGmLjcABjPNIaLelWfmSq7DvwK9C021EagnpiuR0aMvIpUAKvU129tnyzxgHgZrlqvU76MbIu25EkGSCAwzQtnCH82QcAYwasIoCLjoKnC7sHFYmyM+HTo2lWaRACfm29ga0xGhkSLHyoc8ev+TQflQYXJzxVmGDoe/rS3G2JcqfszovG4beOOvH9at2sKxxqBwAABT47dWIzzjmrsduAM8VSTM2xkaqRjFTqvFSKm0dBTmPQVRFyIL04pHQ1IDzSnimBUePgnNQsPar5Xiq8iVLRSZSkXioCMDJq1KpANV2XioZZXc8+1RMQOakkBqu55pFaCSEdzWBrEatAwx+FbbHNZ2pRl7c8VSJZ5VqEP71yRjFZRBAOR2rf1Zds7DpiseVTtOBweld0HoedVVmQIcEYq5bx+bIFXO49MVRBKsB3q5buVkR1JDA5BFWzNHufwtvjJYNDIm0ocZB7YGK9QXpXBeBIYp9JgnHzErg56+uDXdRghQDUAyUU6mZpwNAhaWkpc0ANzSUUhOKAGsSOleS/F0v9kjdmwAcAfga9ZY8dM15B8WrRpIknO7jj2HWkNHjbNvlDipEOFI/E1BkAt+lSMTtT35NaCHkkIGJ60yAebLsHTNEhxGB2FXdEtTPPtHfkn0qZOyNKau7HT6Ha7sADgV2lvAPLCkdDmszSbQW8ICituL5Qa4ZO7PSUeVWHBeMVOi/KKjZlRdx60jXqRpk9+gpJXBssuUi2Fj05py6hAB94ZNcrrWriCD5Tl2wAM9K4+516RHKpI2foMVpGDZlKaW57Jb38LN98E+lasMqlRyOa8Di8TXsThw+0Z6Yrf074gSxFRLuPTI4GfpxVOm0Sppnse8470NzjmuQ0rxfHqCAgbfYkZro4rtZUyMZPvUl2LSqSal2kjnpVRJiDjOB71MJ9ox2oEyUioJADxmq91qcdujd65XVvFyWo2BQc8/eFGgJM6OeZRkcf4VQe6jBwXxXnV543YuTGMN7kGsifxPeTksDx2A//AFU1TbB1Io9Ve7iJxvXNQswYZXBz3rzCHxFLOQshKOO7Dj9K6LS/Ebb1jmjIB43dv8n+tTKm0UqiZ1IHXiorlN0RBpYrqOU/KaklOVqC0eaeI7No5WbB2nvXMAkqUPavTdctfMgYkda83njMN4VP0rqpO+hy4iPUqSLmQYHXmrdptMwVlyCefX8KhnUK64rS0i1+2XawDhnHBHUEc1scp7r4DiktdOiXO6IINrY7YFd2hyK53wvbPbaRDC4AKgdOh4HSuhQYGBUiJRThTRTgaBDqKQUtADaaacaaaAGNntXnPxQhkk0UhY8jdlmH0Neisa5fxra/bNBuk4CrGWLfgeKQ0fM0igMcdPWnfeTnr0p0qfvCgzjNMddoUCtEAxzkbcd667wtaAIGI5NctGgeQCu78NIBGMD2rKrsdGHXvXOrt49kYyKs8Iuc1GgIQU51YxDjk1xna2Zt3eMzlEVmwcDFRSRTSlSzEAD7oOSTVxLYR8kAtnPParCBR259aq9gUTmrnQLq7ZmEZJY5y3OKZZ+AA433PXPbn+tdvG8UYGT83oBzVyJy6HEP/jwFNTZMoLscZ/whFki4Chj75qhN4RgjbHlYwfSuxubxEYhgUI/GiKaOcBWIOaXMy1BW2OWstH+yMTGzD0xXW6ZcPhVYnOOeahmtQnzL0pLUhJKltlWVjpUAMe41FNNtjx0qWz+ePqD7YrNv2KlsfzpvRGSV3YzdRuA2QCc5rk9R0r7Y5Y9TXQTEySHPWnw2RfDOvB9aSbNXFdTiYfCHmOWxn8OKvL4JITPfHpj+tdsghj4AA7VYUoycZP0FXzyM3CPY85HheaFiWjz9DV1NIm8kKYEIx7V107x5wDg/TFU3HPXA9aTm2UoLoYKxXlsFZCRjsTwa3ba4WeHcD7EehpvlqeCAQfai3gEW/auMmgl6Mq6jGGgbjtXmWtRbLwEDFeq3iZibjtXl+tkG9IrWluY1n7pmugZvcgGt7wvZSXuqRpEGO0AnaOeD29+9YLf65celehfC+BG1KR2Ulh93nAHXP6V0M4j2bSjus4nzyVG4ehxzWkpqnbRqgGwYHpVtaQmTLThxTBThQIfnNOzTBThQA000040w0ANasfX036TcrtDbo2AH4Gtdqq3EayRsrDIoGfMmu6dJp18UkVVJAOF+lYrtkjbXp3xH0WWfUgbaBiqRc4HsK8vdHglaORCrA9GGKcX0G09ye3I3A9+ld14YUsma4SBduWPY5rv/AAf88ZP+elRW2NqD947KKLK05lwKniQAe1OKA5OK5DsuUJFGOKzbu9itFLyPtArXngJQ7eDXPX2lM775CSc9qS3NVYzb3xRcLE32KLAXksRz/OsLTfEt5f30kd3rS2EYjZ1dw7BmHRAFzyffj3rtNPsLaKJ4ZIiyOPm9a5m88BTNeM1m8RhJyPMYgj9K6Ichy1vaFLR9c1TU7xrfzQ+BnJzzzXSWt5NDdLBKCkvp2P0rS8L6HbeH4pJLlvMuXXb8vQD2q7ex2d0pka2uvM52HC4BwMEe2c/pUzS6F0ZT2kXLWYz2x5zjrTIgd/0pumLMbFmddj5wwI6+hq3bxAR5OefWsWje5tad8vOeorO1TKhj2zV+ybYBVLV+QcdKHsQviMGHElyo9TV2+m8iMKvXFV4Excpj2p2seWGCS5AdTkLnJHse3/6qcUW9zmb7xAliHlYPIEODgcCi08ZanPpFzf2mmq9nbNsllZsFTjOMZ54roJ9C03UfD0ljBIiuQGXLYOeDz/KvKrrw7qNpO8D284UnHCkgitoRi9zkqVZ30OusfG/28lpLVgFxnbz/AFrYtNTtr5SYHPup7VleFvC4sdPe4vwFkkIwmfugE1VuNLaHU2uLTKruzwamUY30Nac5vc6tSAauoAy571j2LzOoEqnI74rahGFrO9jSS0Kl4u2Bz7GvJNVy19Jk9K9i1JP9EkI/umvHNW5uZcHvW9Hc5Kz90phwZOPpXsXw10x7ewFxIo+chgfYj/6/615To2my3+o28KqcO45r6G8O6aun6bbxKoAVAPpwM1uzlN+IYFWFqCMVOtIRItPWmLTxQIcKWkFLQA2mmnGmtQBG1RMKlNRNQM4rXLqIas9vIMErxk9eBXH654Sgv/34UBhznmuq8Xaf9p1GN1JDKwII4PaqyTfuBG/3lGDmuSTaloejTS5EeOXtsbS8mhP8BwK7PwYP9HBx/nArC8VxINYkK8AgH+ddJ4RCC1GPxreTvBGEFao0dnDzVpEzxVaDtWhGPWuax0IhMWTVe4shKvIrUWMHnHNPMXHIp2BNnNmwkU46ge1SJbSJ91Dt5yMmt3yh1xSeVzjHFFi+cw/IuHiEbMxAPpilit5AArE4HAAFbQhBOAKPs+xSSBmmybmc5YAcY7AVLHFiHJp/l7pasTqqRBffmp3GFqPmHHFO1G2DIT6iltMbh9anvWBBFO2gX1OXC+WytjlTzV9HSYZKgE9Ce1V9gMzL2NSwDGV9Km5b1Rn3STRMfkDD6VF57MAnkDbnpk1uvEJOuPyqJoUH8Iz9Kq/Ym66nPSwM5YFjjsAKWKzJICrgVtfZwTnFTJCF421NynLQoJa7cZqyqYGKteUKPLpENlG+iMljIg/umvFrqB5NQkj7hule6soKMD3FeTJbkeLWi28F8H6cV0U3ZM56ivZHS+B9OltbyBnAK9hjkV61EMKBiuQ8PW6m8+UDEQBP1NdjGOM1cG3qzGsknZFhKmWokFSqK0MSQU8daYOlPHWgQ6lpBS0ANNNNPNMagCNqiapmqJqAOd1yH9/HLjgjB4rmdVjWOIzKSCBmuv1cCRoo/qa5TXR/o0kQHJBArlqfEejQ1geW65Mt1clwemRn8a6HwYymHAPTr+lZes6R9hRQ/JkG4EUnhGdrfU/JYkqwP51qtYGT0qHp8bYwMVdifnmslJDgVct3yc5rnehujWifipgd2Kpw8irEY2Himhk2zAz2pBtzt21IvPWnYHTsKokTy+46Y9Kgm+6TntVtQMe1Urr5Qx9aTGtyh5gDnmh3MrcdKqgl5iB0zVuFfmxSLZftIuN2aS6HHI5qe3wqgH1pbhQ4NVbQi+pz0mFn3YpI/wDWZqxdQkEnFUQ5WUKePSs2i0asY3LzxTjEGGcdabbHOOaubRjmmhMp+SAMY5pGXGKsyDIA7ionxk46UmBGBUTsAacxI5qrKxAyTSAeGzkV5rCm7xjcvn7nP1PFegiXarMegGc153oW+51q5ujnYx4/P/61bx0jcyavNI9U8OQhRM+PvlT+ldHGK57w9KvksncYro4xmtIbHNXXvsnUVKoqNKlWtDEeBThTRTgaAHUtIBTqAGmmGn000CI2qJqmaoWoGYN/IRfPnoq8ViXKrOwDdCea19aIhuldjhXGK5+7imjDtH869eK5Z7noUGuUz/EOmrc2xCjJUZU+hrgbKNrTVI/lIYZya7yC9dmeGVXGehYYxXN6zbGO5hnUdDtzThK2gVIdTqbdiyqQa0IXIOKxrBibeM+1acb9PepkhxZsRP0INWklGRk1kRuVI9KtI+e9Z3KNVHFP84KwBNZ6yHORViP5gN+M1aYrF5WBBx3qhfAiJucdgKuKOPQVmaxN5QT/AHqp7CW4yCBI13E9euaaZlhkJz1rJvtWjtLVpJHwoHrXFv8AEK1eYxy21ygzjO0UtXsa2S3PXbW4haPls5p000YizuGa4rS9ahuIA8UoZenUVbm1JDGctwB3NHMxcmpqTXaO20nvTHtop0yMBh0Ned3/AI7tbW/aBIJpypwWQDH4etdTomvw6lah49ynurcEGhp9Q0ex0Fqp2jjkcVeyAlZtlOHlYe1XHYqtLYTFZ+Oagdsd8UySTNRM/FQwsOdx0BqlM5qR3xVVmyaaBlHV7g22k3MmQCUKr9SKxPDVmYIAP4jhjnv/AJzWhr7b4o4cZXO41FbTCy2wqpeRl6DtWrdlYiCu7nZ6KB9q4H8NdNHXLeHw5kVnGPlrqox0rSnsc2J+MnWpVqJalWtDnHilGKQUooAeKWm0o6UAIaaacaYaYhrVC9Smo26UDMTXrI3dl8o+ZORjrXM2Vy9uGgm+ZgflLdxXcyDcCD3rltTsFSclRg5yDWFRdTpoT+yyjdtFImRGob2FcrqMbZ2OvfPNdb5P97tWNq0aFkK4OOOlZLc7HsMshiBRirkfUVWtThcVZXhqpmSL0JBFWV68VSjbFXIjxzWRZYUndkA1ZRjVePGMjrUoBzxTQF5WIXOetZ2rwNcWbqn3+oPvVtSdo5ok5XFWJaM4B4HknEV7G20HjI4NWp9D0ifaRYxg4xnbXSTRxFiXRTVd4lOSBgDgAUXNedPc5lvDsttJ5lhwCOVFNGkX1yCkxZU/iJ7j0rr4Yiq5GeKUrkHPpRcXOcamh2MMwMlqjBTknb1qST7PbTf6HDtJ7KK3pIU38jOadHbW6kEIM/SlzFqSDSI5VQySj5j1HpWpK3ycVFEAFwKc44PFSZt6lUvUZJzmnMuDUbnAqQI2bk5NQM3zYzTnamDHXvVxJkZerZM6DGRxV+wtLXeLh1BlPc1BcxCe5UHoBV61i8rj+dVLcI6RudHo6/v5WC4UYA/Kt5BWbpsIjtk9W5J9a00roirI4KjvImFSLUa1KtUZj6WkFOFMQtLmm0tAxD0phqQ1GRzQIYajapTUbUDK71n31qLiPGcMOhrReq796TVxp2ZzkmkXDNhplC+3Wo5dFthGVwzN6k1vSVVkqeVGjqyZyMkP2dyp60qn5sVo6pCA+8d6zFOc1jNWOmlK6LaHjmrMb8Yqij7RVhG71ibF+LC5IPWpkcg57VUjb5aVpTsoEXTcqB1qNbjfkbs1lyOy4YNn19qmtZlZQOMCquTa5dMZYg0vkd6pz6zbWgILbmHYc1iz+I55ThHVF9AKdi4wbOqCMFzihVJBUA1y9vrU0LZWbJPXdzVu58QXDxBU2JkfMVHWnYv2bNV4ckjg4NQPGVb2rnTqMkbblcg1etNc3fLcYx2YdqTiKUGjV87bjBqTz1c4BqnNMrICpBU88VAkpXGOakjUvPgciq0jZzinlyYwT1qBzxRYaIzzTO1O78U1Rl8VUUTLYf8A2dcysJYQCB2zg1p2ljcSyL5ibVB5yat2ceyNRWnHW/ItzkdaVrFiFQihR0HFXEqrH2q0laGJMKkFMWnigQ8dKcKaDThTELilxQKWgYw00049KaaBDGqNqkIpjUDIGFV5BVphUDikBTkFVnFXHFV3FAzKv498LCudLbWrq50yD9K5W7BSQ49ayqI6KD1sODVYifIrPWUZqxG9YNHWaCP6UkrMozmq6Sc5p5bfwBUghk3mNGSqgntXM3Nzq0EjxrA21jw2P/rV18UfPzdKLuNXTAA9qpNdSjj7eyv7vJcqmT945NakHh9SgDTNuxz71c5h6jjPapftsaj75U1aki02V/8AhHdicSP+VRpoEzKFNyxUHj5a0RrsMaFGdT7mg69AQApQcetVdFKUjOm0F1Tmdh9VqjPptxCMpIjjHQ8VrTamJiMvxnOBVeR/NPGaV0J3MO31G+E32UW8jZ744FdLAreUN/DUyytQkocqM+tWZRg8dPSs5NMiwK2aY7DNJuwMCo2brQiGDt6VLZAvOoqo78itPSo8sXI9q0gtTKo7RNyBcACr0fWqsQq5GK6DhLMfBqwhqCMVYUUASrzUgpi08CgQ8U4U0U4UxC0tFGaAGnpTcU4000AIajanmmEUDImqFulTsKjYUgKriqzirjiq7igZSlXINcpfgLdsh6N0rsHWuU8RR4dHXj3qJ7GtL4jN2c1IhwOtQwzCZNwPzDhhUue2KwaOxMnU4FWYjxVNeCPSrcQPWoZaLQYgUpIbrTVGaftNSMgkgDdqrtZIR93JrSVDinfZt46CmhXMR9LSQ52j8qjOkR9cY/CugNswHamtBhcmnqNMxFsFUfdH5VMlsARxV9o/QVERjtSuUIgCjmmtg9TSnJ6Uwg0CIG4NRseDU7jAOarOatGbI9pdvaulsIPKhUdz1rH0+3MsoPYV0kS9BW8Ectad9CxEOlW04qug4FWo1rQ5ywlTrUSDFTLQBIoqQU1acKBDhTgabS5xTAdRSA5paAG96Q0tFAhpppp5phoAjao2FSkVGwzQMgfpUDirLCoXWkMqOK5zxFF+4Q47mumdaxNfTdZA+jf0qZbF03aSODkZreXzE/EetXoblJk3Kfr7VBcR5zWeC8Eu5OKwXY7muqN1ZeavW7g4BIxWFFeBsHGGHWrkNztKkGlKAoyOkhQEcYNWFjHcVl296AcHpWhFchuv41FirllIR6VOsage9QpMFPrTjMEbcOppolskeLceeBimPHGigtnr3pkl4oIyfwqu1wpJ9PrVaBqJIi5OPXiojEKc0wbp1+tJ5ox6Gs2XcjMIHSo3VQCae9wMmqNxccEmmkFyKeSqbyqFLMcAVHcXAwSTgY5rMkuWu5gB/q1PArVKxF23Y63Q381XPbIxXQxLXN+HBhWX6V1ES1tDY5KqtInjWrMYqGMVZQVRkTKKmXpUS1MopiHiniminCgBaMUopaAEApcGlooAbiiiigQGmGnGm0ANNRmpDTDQMiYVC44qwaiYUDKrjNZOsJvsJPbmthx1qjfoGtJhj+A0mtCo7nn8idaozRZbpxWnIvzEVC0YIxXIelHVGPKhU5GaSO5aNsN0rQlh5xVGa2I6VcZESgXYb47shulakF+XxhuPY1yUglhGVJ+hpkeqNE3JINNxTJvbc7xb4hOSQe1P+3lucnniuMi1ssMFv1qX+1h/C35mo5GUmmdPLch23F8djQb0FPkbr1NcydUB6kYpBqALfKxo5WO50i3WTu39KVr0AEk1zbangYJqN9UAGSeKOQTZ0DXoOQWxWfdagOm78jWFJqjSEhOaageU5YkmrUUidWWprh5yFBOPSp7eMqyYHFMtrf5uRWpHEABj+VS5GsY2Ru+H+JyPUV1cYrkNFbZeR8+v8q7KMZranscVde8SoKsoKhQYxVhRVmBKoqQDmmLUq0xDhThSClFAD6KBRQAtFJS0ANooooAQ800049Kb2oAaabinnFNPSgCM1G4qU9KjagZXbpVO4XdC6+qkVdcc1WkXIpMaPPrhcSuPQmoTwKt367LyZR2Y1X2g9ua5HuenB3ihhQOOeKrPGc4NXSCozUUmGOM0DZSe2DA8VmXWlCRWIHNbykcg9aeEVu1O9ibI4OfR7hX+U8fWqxsL9OjN/wB9V6OLNJOq5qQaREwGF/Wq52LkR5kYdRX+JvzpANR7Mfzr0qTRogPufrVJ9JXn5BR7QPZo4IrfHhpMfjU8NlK3LuzexNddJpUeMbRikFiqEYFPnH7NGTa2TKBuArSjgwQAOatLD2FSrHj61LkUo2Gww4wPzq6q4HSmxpipl5qGWyW1fyZkc9Ac12tvIHiRx0YA1w2cHI7Vp+C9XN59q02Zh51s7BR6qMYrpw+uhwYqO0jtEFTrVONyhwauIQehrVqxy3JV61IKjWpBSAfSikHSlFAD6KSigB1FJS5oAbSUppMUAITSU4ikoAYaSnkU0igBhqNqkNMagCuwqvKQqk+gqy/ANULpitu555BH6VUVdg2cNfSiS9lcdCxqDoQRVZJN80wJ6SMP1NWUPHJrjqL3menS+FEgIZcdKgmG05qdV4yOtMkXINSaFcNnBzyKkRgxGQRUKjDVMmD1oETI5RuDWhbzc/0NZTRn+FqI5pI3G7JFAG3M+R0qjI3PTFNN5vXCgk0wB36kLQAjgEdRVZwCwA5q55Sgdc1E6c8ACkUV9oHQ5NAU08qB3pY1LewoAeiH1p7DaMA08YUYprdKQyF22isKw1E6N46huP8AllOmxh05571uSDIrktaG3UrWcj7kgz9M1rSlaRhWjeJ7m4yFbIIbkEU6ORkPy/lVWwkabTImYHcOCcenvipwRjpXoWTPK2La3fzYKH8KnWcd1YfhVBOtW42BXBPNLlQ7stK6t0Ip4qkv3vQ9qlUuP4jUumO5aoqESEDpmlE6H1H4VPKwuS0ZpqurdGBp9Sx3EpKWkoEBpDS0hoGJTTSmkNADDTG705j6VVml7CqUbiuRzMS2BVS8mWKDDAHIOPft/WrDDnFU9TRnNuycqjc1qkib6nmTsY9Zvo+g8zP581cjfjmp/EenLZ6x56H5JwCPwqihNcNZWkeph3eCL6MetPbDdetQR1ITisjcrvGQT1pVyKm4PB6UzyiDlelAiRenWgxlqdGCKnSLcc9KBFWNSDirSqAMHg/Sn+SFOe9KyEc0gGsMA1WfrU7gmoChVvmz9KBke3c3tUqccDpTSecU9SNuO9Axx60xuRTicUhOaQyCQ4U1y2sxl1ZvTnNdPMRgisi4tTczJEOrtgVUNxS+Fnpnhtnl0r95977wIHUH8P61ojk1X09408u3xhhH0Ht+HvVkfezXpx2PFluPA5qdRkZ71Eo5qZc9aYh8YJJYjGKm5PbHeoO2V61Mpyq8g/40CFI96gbKmrGexBqu5BJHcdaYCBueOD7VKLmRRjIqupyDgc9ulSCQY7UmgNCkopDWBYUGkJA60wyZOBQk2ArHFRPJ2odsIWxnHpUBxvViCc+laxgkS2PkYqvJFUy2W461NM248dBVYHmqYh3Q9OafIymEh+cgj9DTCMjk0sisZoHxwOTnvyB/U0IDh/GWYxbH+AMQpx24rDhIKjp0rtPGGn/atCmeMfNEA+B9Bn+VcDp8++LB6jiuPEL3rno4V3jY01bFTDBHvVYNmpUbI5rmOscxAoD4HBxSdRzUZ4OKALcbBh82Pwq9FtwMHmscNtNWIrjBoCxsBFI6k01wMcCqq3HFI0u7vQSLIQAc1UkIzxUjuTURA2nJzQUR7iTxUsYqNV5qYLigBccUwnGaczYHFQO/HNICOUgiq1s6xarbPIDtDdvoaldhRp8bXGpxIg5ySSRkAAGrhuhT+FnodlHHJKbhugITk1c24c4xx75qtbxuFjMZbYCMgcHj/wDX+lXwAeBG/wDn8TXpo8VvUROKkHINIFIXJBA+lIGBYhf8f5UAP3YpWJUMVYjI64pnQ85py/MCO3vQIXc6KoAyc4J/GoppGXdjGD1yeafubcwJGDjn0z/+oVUkYvcBD2OT+mf5GmA6FGkYsCcnp+QI/rVoRRtnKDAOB9KVMQp/u4A/A/4GnQksuc+n8hSbA//Z",
                "regPlaceAddress": "Phạm Thế Hiển,, Phường 4, Quận 8, TP.Hồ Chí Minh",
                "religion": "Phật giáo",
                "wifeName": "",
                "chipAuth": false,
                "chipAuthMessage": "",
                "cscaAuth": false,
                "cscaAuthMessage": "",
                "sodBase64": "",
                "hashCheck": true,
                "husbandOrWifeName": "",
                "husBandOrWifeName": "",
                "homeTownProvincesCode": "",
                "homeTownDistrictCode": "",
                "homeTownWardsCode": "",
                "addressProvincesCode": "",
                "addressDistrictCode": "",
                "addressWardsCode": "",
                "addressDetail": "",
                "cloneChip": "No",
                "valid": true
            }
         }
    }
]
export const FakeVerifyNFC:any = {
    status: 200,
    data: {
        "status": 200, // trang thai API
        "message": "Success",  // Nội dung trạng thái API
        "exitCode": 0, // Mã kiểm tra
        "exitCodeMessage": "valid DSCert",
        "activityID": "l1428k-76427952-21ea-439a-afb5-d9c6cdc97d13-k1727w-b15364n", // id from gateway FIS
        "data": {
            "result": true,
            "time": 1727056771,
            "signature": "dLgtZSMIPQluq0T7LGVE74CqAzdVIl+n8JroeBV4Gpw6ZfILfp+QDA9+LlHvVG2LNYREcNzMsDcILCZlHcOXMmpXfJWvphdTc6hDm8PPluylBjuB+zM8RedUL64FHYFufrrxDXSJFEAtLhAkBb6TKZ0qC1wGlDrlKBy0rILM/TszW4qQ701QjKzOJvkyedkIpd8oWnHnjMv5MPvVlm16P8PlFXNNK17ikfKfs205TI6X9r69NVF7EGCU/yAYMT1Z733oF2JxdhLPe4UFxnFB2SRKfaO+qxFKj4CnZF1ssTscIXGT9Aq9y2Rxt5fQWinYsGEbVKt/FZmqTDgwAA3VkQ=="
        }
    }
}

export const FakeLoginNFC :any = {
    status: 200,
    data :{
        "accessToken": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhbHRpc3NfcG9jIiwidXNlciI6IntcInVzZXJuYW1lXCI6XCJhbHRpc3NfcG9jXCIsXCJ1c2VybmFtZV9wYXJlbnRcIjpcImFsdGlzc19wb2NcIixcImJpbmRpbmdzXCI6XCJcIn0iLCJzY29wZSI6ImNoZWNrX25mY19iY2EsY29udGVudF9tYXRjaGluZ19vY3JfbmZjLGRvY19uZmMsZmFjZV9tYXRjaGluZ19uZmNfbGl2ZW5lc3MsZmFjZV9tYXRjaGluZ19vY3JfbGl2ZW5lc3MsZmFjZV9tYXRjaGluZ19vY3JfbmZjLGhhc2hfY2hlY2tfbmZjLGxpdmVuZXNzLG9jciIsImV4cCI6MTcyNzE0MjAzMX0.U_g-B2C6eJXvC53AxC0Dukv6hUYHMr0LoX54BxwBtZBWfwIkU2-MhOEa1wokx62_RGekB7dWWg162Z_jNjDc_g",
        "refreshToken": "",
        "scope": [
            "doc_nfc",
            "check_nfc_bca",
            "hash_check_nfc",
            "liveness",
            "ocr"
        ],
        "tokenType": "Bearer",
        "expiresIn": 86400,
        "status": 200,
        "message": "Success"
    }
}
