import fs from 'fs';
import path from 'path';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import sharp from 'sharp';
import logger from '../config/logger';
import uniq from 'lodash/uniq';

const EKYC_ISSSUE_DATE_PROB = process.env.EKYC_ISSSUE_DATE_PROB || 95;
const EKYC_ADDRESS_PROB = process.env.EKYC_ADDRESS_PROB || 95;

// Nhacvb-7282
const TYPE_FRONT = ['chip_front','cc_front'];
const TYPE_BACK = ['chip_back','cc_back'];
const CARD_NEW_FRONT = 'cc_front';
const CARD_NEW_BACK = 'cc_back';

interface dicSecInterface {
    [index: string]: string;
}

class globalService {
    public LIST_SEC: Array<string> = [];
    public pathEKYC: string;
    public dicSec: dicSecInterface = {};
    public pathReport = '';
    public pathReportOther = '';

    // public dataReportDetail: Record<string, number | string> = {
    //     // số request client gọi api ekyc
    //     client_request_total: 0,
    //     // số request server xử lý lỗi
    //     request_server_error: 0,
    //     // số request sai cấu trúc
    //     client_request_invalid: 0,

    //     // số request bước 1
    //     client_request_1: 0,
    //     // số request sai cấu trúc bước 1
    //     client_request_invalid_1: 0,
    //     // số request gọi fpt bước 1
    //     request_fpt_1: 0,
    //     // số request fpt trả thành công bước 1
    //     respone_fpt_success_1: 0,
    //     // số request fpt trả thất bại bước 1
    //     respone_fpt_failure_1: 0,
    //     // số request pass logic ekyc bước 1
    //     check_logic_success_1: 0,
    //     // số request fail logic ekyc bước 1
    //     check_logic_failure_1: 0,
    //     // số request phản hồi client bước 1
    //     respone_client_1: 0,

    //     // tương tự cho bước 2
    //     client_request_2: 0,
    //     client_request_invalid_2: 0,
    //     request_fpt_2: 0,
    //     respone_fpt_success_2: 0,
    //     respone_fpt_failure_2: 0,
    //     check_logic_success_2: 0,
    //     check_logic_failure_2: 0,
    //     respone_client_2: 0,

    //     // tương tự cho bước 3
    //     client_request_3: 0,
    //     client_request_invalid_3: 0,
    //     request_fpt_3: 0,
    //     respone_fpt_success_3: 0,
    //     respone_fpt_failure_3: 0,
    //     check_logic_success_3: 0,
    //     check_logic_failure_3: 0,
    //     respone_client_3: 0,

    //     // tổng số request phản hồi client
    //     respone_client_total: 0,

    // };

    public dataReportOther = {
        // số request client gọi api ekyc
        client_request_total: 0,
        // số request server xử lý lỗi
        request_server_error: 0,
        // số request sai cấu trúc
        client_request_invalid: 0,
        // tổng số request phản hồi client
        respone_client_total: 0,
    };
    public dataReport: Record<string, Record<string, number>> = {};

    constructor() {
        this.LIST_SEC = process.env.LIST_SEC.split(',');
        this.pathEKYC = this.makePathEKYC();
        this.dicSec = this.makePathSec();
    }

    private makePathEKYC = () => {
        const pathDB = path.join(process.cwd(), process.env.DB_LOCAL);
        const pathEKYC = path.join(pathDB, process.env.DB_EKYC);

        if (!fs.existsSync(pathDB)) {
            fs.mkdirSync(pathDB);
        }
        if (!fs.existsSync(pathEKYC)) {
            fs.mkdirSync(pathEKYC);
        }
        return pathEKYC;
    };

    private makePathSec = () => {
        let dictionary: dicSecInterface = {};
        this.LIST_SEC.forEach((sec) => {
            const pathSec = path.join(this.pathEKYC, sec);
            if (!fs.existsSync(pathSec)) {
                fs.mkdirSync(pathSec);
            }
            dictionary[sec] = pathSec;
        });
        return dictionary;
    };

    public isImage(filename: string): boolean {
        const imageExtensions = ['jpeg', 'jpg', 'png'];
        if (!filename || typeof filename !== 'string') return false;
        const slitFileName = filename.split('.');
        return imageExtensions.includes(
            slitFileName[slitFileName.length - 1].toLocaleLowerCase(),
        );
    }

    public getTypeImage(filename: string): string {
        if (!filename || typeof filename !== 'string') return '';
        const slitFileName = filename.split('.');
        return slitFileName[slitFileName.length - 1];
    }

    public isVideo(filename: string): boolean {
        const imageExtensions = ['mp4', 'mov'];
        if (!filename || typeof filename !== 'string') return false;
        const slitFileName = filename.split('.');
        return imageExtensions.includes(
            slitFileName[slitFileName.length - 1].toLocaleLowerCase(),
        );
    }

    public getTypeFile(filename: string): string {
        const slitFileName = filename.split('.');
        return slitFileName[slitFileName.length - 1] || '';
    }

    public getPathUser = (sec: string, userId: string): string => {
        const pathSec = this.dicSec[sec];
        const pathUser = path.join(pathSec, userId);
        if (!fs.existsSync(pathUser)) {
            fs.mkdirSync(pathUser);
        }
        return pathUser;
    };

    public checkPathUser = (sec: string, userId: string): boolean => {
        const pathSec = this.dicSec[sec];
        const pathUser = path.join(pathSec, userId);
        if (!fs.existsSync(pathUser)) {
            return false;
        }
        return true;
    };

    public getResModel = (
        req: any,
        codeALT: string,
        result: any,
    ): any => {
        const fixResult = cloneDeep(result);
        if (req.step === '1' || req.step === '2') {
            fixResult.result = { ...fixResult.data[0] };
        }
        if (req.step === '3') {
            fixResult.result = { ...fixResult };
        }
        return { ...fixResult.result, codeALT };
    };

    public saveFileJson = (infoReq: any, resEkyc: any) => {
        const pathUser = this.getPathUser(infoReq.sec, infoReq.userId);
        let pathStep = '';
        if (infoReq.step === '1') {
            pathStep = '/front_id';
        }
        if (infoReq.step === '2') {
            pathStep = '/back_id';
        }
        if (infoReq.step === '3') {
            pathStep = '/video_selfie';
        }
        const fileName = path.join(
            pathUser + pathStep,
            `/${pathStep}.json`,
        );
        fs.writeFileSync(fileName, JSON.stringify(resEkyc));
    };

    public checkEkyc = (
        step: string,
        SecCode: string,
        obj: any,
        isImageFaceMatch?: boolean,
    ): any => {
        if (step === '1') {
            return this.checkEkycStep1(SecCode, obj);
        }
        if (step === '2') {
            return this.checkEkycStep2(SecCode, obj);
        }
        if (step === '3') {
            if (isImageFaceMatch) {
                return this.checkEkycStep3FaceMatch(SecCode, obj);
            } else {
                return this.checkEkycStep3(SecCode, obj);
            }
        }
    };

    private checkEkycStep1 = (SecCode: string, objRaw: any): any => {
        const obj = cloneDeep(objRaw);
        if (obj.errorCode !== 0) {
            const error_msg = obj.errorMessage
                .trim()
                .replace(/ /g, '_')
                .toLowerCase();
            obj.data = [
                {
                    errorCode: obj.errorCode,
                    errorMessage: [
                        error_msg ===
                        'unable_to_find_id_card_in_the_image' || 'quality_check_failed'
                            ? 'front_image_id_invalid'
                            : error_msg,
                    ],
                },
            ];
            return obj;
        }
        const details = obj.data[0];
        details.errorMessage = [];
        details.errorCode = 0;
        if (
            !details.type_new.includes('_front')
        ) {
            details.errorMessage.push('front_image_id_invalid');
            details.errorCode = 1;
            return obj;
        }
        if (
            details.dob.length === 10 &&
            // @ts-expect-error
            moment().subtract(18, 'years').startOf('day') - moment(details.dob, 'DD/MM/YYYY').startOf('day') < 0
        ) {
            details.errorMessage.push('ekyc_user_not_18_please_contact');
            details.errorCode = 1;
            return obj;
        }
        if (
            details.dob.length === 4 &&
            // @ts-expect-error
            moment().subtract(18, 'years').startOf('day') - moment('01/01/' + details.dob, 'DD/MM/YYYY').startOf('day') >=0
        ) {
            details.errorMessage.push('ekyc_user_not_18_please_contact');
            details.errorCode = 1;
            return obj;
        }

        if(CARD_NEW_FRONT == details.type.toString().trim()){
            // kiem tra dia chi thuong tru  EKYC_ADDRESS_PROB
            // NhacVB R3360 - Add rules issue_date_prob
            if (
                Number(details.id_prob) <=
                Number(process.env.EKYC_ID_PROB)
            ) {
                details.errorMessage.push('id_mo_nhoe');
            }
            if (
                Number(details.name_prob) <=
                Number(process.env.EKYC_NAME_PROB)
            ) {
                details.errorMessage.push('ho_ten_mo_nhoe');
            }
            // if (details.checking_result.edited_result === '1') {
            //     details.errorMessage.push('front_image_id_edited');
            // }
            if (Number(details.checking_result.edited_prob) > Number(process.env.EKYC_EDITED_PROB)) {
                details.errorMessage.push('front_image_id_edited');
            }
            if (
                details.checking_result.check_photocopied_result !== '0'
            ) {
                details.errorMessage.push('front_image_id_photo');
            }
            if (details.checking_result.corner_cut_result !== '0') {
                details.errorMessage.push('front_image_id_corner_cut');
            }
            if (details.nationality_prob !== 'N/A' && Number(details.nationality_prob) <= Number(process.env.EKYC_NATIONALITY_PROB)) {
                details.errorMessage.push('nationality_is_blurred');
            }
            // Nhacvb-7001
            // NhacVB-7282 TYPE_FRONT
            if( !TYPE_FRONT.includes( details.type.toString().trim())){
                console.log('details.errorMessage: ', details.errorMessage);
                details.errorMessage.push('only_chip_id_card');
            }
        }else{
            if (
                details.doe.length === 10 &&
                moment(details.doe, 'DD/MM/YYYY') <= moment()
            ) {
                details.errorMessage.push('front_image_id_doe_expired');
                details.errorCode = 1;
                return obj;
            }
            // kiem tra dia chi thuong tru  EKYC_ADDRESS_PROB
            // NhacVB R3360 - Add rules issue_date_prob
            if (
                Number(details.address_prob) <=
                Number(EKYC_ADDRESS_PROB ) || 
                (details.address_entities.province == null || details.address_entities.province == '') ||
                (details.address_entities.district == null || details.address_entities.district == '') 
            ) { 
                details.errorMessage.push('address_is_blurred');
            }
 
            if (
                Number(details.id_prob) <=
                Number(process.env.EKYC_ID_PROB)
            ) {
                details.errorMessage.push('id_mo_nhoe');
            }
            if (
                Number(details.name_prob) <=
                Number(process.env.EKYC_NAME_PROB)
            ) {
                details.errorMessage.push('ho_ten_mo_nhoe');
            }
            // if (details.checking_result.edited_result === '1') {
            //     details.errorMessage.push('front_image_id_edited');
            // }
            if (Number(details.checking_result.edited_prob) > Number(process.env.EKYC_EDITED_PROB)) {
                details.errorMessage.push('front_image_id_edited');
            }
            if (
                details.checking_result.check_photocopied_result !== '0'
            ) {
                details.errorMessage.push('front_image_id_photo');
            }
            if (details.checking_result.corner_cut_result !== '0') {
                details.errorMessage.push('front_image_id_corner_cut');
            }
            if (Number(details.address_prob) <= Number(process.env.EKYC_ADDRESS_PROB)) {
                details.errorMessage.push('address_is_blurred');
            }
            if (details.nationality_prob !== 'N/A' && Number(details.nationality_prob) <= Number(process.env.EKYC_NATIONALITY_PROB)) {
                details.errorMessage.push('nationality_is_blurred');
            }
            if (details.doe_prob !== 'N/A' && Number(details.doe_prob) <= Number(process.env.EKYC_DOE_PROB)) {
                details.errorMessage.push('doe_is_blurred');
            }
            if (Number(details.home_prob) <= Number(process.env.EKYC_HOME_PROB)) {
                details.errorMessage.push('home_is_blurred');
            }
            // Nhacvb-5323
            // Nhacvb-6261
            // if(details.id.toString().length == 9 && details.type == 'old' || details.type_new == 'cmnd_09_front' ||  details.type_new == 'cmnd_12_front' ){
            //     details.errorMessage.push('cmnd_9_expired');
            // }
            // Nhacvb-7001
            // NhacVB-7282 TYPE_FRONT
            if( !TYPE_FRONT.includes( details.type.toString().trim())){
                console.log('details.errorMessage: ', details.errorMessage);
                details.errorMessage.push('only_chip_id_card');
            }
        }
        if (details.errorMessage.length) {
            details.errorCode = 1;
        } else {
            if (details.dob && details.dob.length === 4) {
                details.dob = '01/01/' + details.dob;
            }
        }
        return obj;
    };

    private checkEkycStep2 = (SecCode: string, objRaw: any): any => {
        const obj = cloneDeep(objRaw);
        if (obj.errorCode !== 0) {
            const error_msg = obj.errorMessage
                .trim()
                .replace(/ /g, '_')
                .toLowerCase();
            obj.data = [
                {
                    errorCode: obj.errorCode,
                    errorMessage: [
                        error_msg ===
                        'unable_to_find_id_card_in_the_image' || 'quality_check_failed'
                            ? 'back_image_id_invalid'
                            : error_msg,
                    ],
                },
            ];
            return obj;
        }
        const details = obj.data[0];
        details.errorMessage = [];
        details.errorCode = 0;
        if (
            !details.type_new.includes('_back')
        ) {
            details.errorMessage.push('back_image_id_invalid');
            details.errorCode = 1;
            return obj;
        }
        // Trường hợp: CARD_NEW_FRONT
        if(CARD_NEW_BACK == details.type.toString().trim()){
             // NhacVB R3360 - Add rules issue_date_prob
             if (
                Number(details.issue_date_prob) <=
                Number(EKYC_ISSSUE_DATE_PROB)  
            ) { 
                details.errorMessage.push('back_image_issue_date_invalid'); 
            }
            if (
                Number(details.issue_loc_prob) <=
                Number(process.env.EKYC_ISSUE_LOC_PROB)
            ) {
                details.errorMessage.push('issue_loc_prob_low');
            }
            // if (details.checking_result.edited_result === '1') {
            //     details.errorMessage.push('back_image_id_edited');
            // }
            if (
                details.checking_result.check_photocopied_result !== '0'
            ) {
                details.errorMessage.push('back_image_id_photo');
            }
            if (details.checking_result.corner_cut_result !== '0') {
                details.errorMessage.push('back_image_id_corner_cut');
            }
            if (Number(details.features_prob) <= Number(process.env.EKYC_FEATURES_PROB)) {
                details.errorMessage.push('features_is_blurred');
            }
            if (details.mrz_prob !== 'N/A' && Number(details.mrz_prob) <= Number(process.env.EKYC_MRZ_PROB)) {
                details.errorMessage.push('mrz_code_is_blurred');
            }
            if (
                Number(details.address_prob) <=
                Number(EKYC_ADDRESS_PROB ) || 
                (details.address_entities.province == null || details.address_entities.province == '') ||
                (details.address_entities.district == null || details.address_entities.district == '') 
            ) { 
                details.errorMessage.push('address_is_blurred');
            }

            if (details.nationality_prob !== 'N/A' && Number(details.nationality_prob) <= Number(process.env.EKYC_NATIONALITY_PROB)) {
                details.errorMessage.push('nationality_is_blurred');
            }
            if (details.doe_prob !== 'N/A' && Number(details.doe_prob) <= Number(process.env.EKYC_DOE_PROB)) {
                details.errorMessage.push('doe_is_blurred');
            }
            if (Number(details.home_prob) <= Number(process.env.EKYC_HOME_PROB)) {
                details.errorMessage.push('home_is_blurred');
            }
            // Nhacvb-7001
            // NhacVB-7282 TYPE_BACK
            if( !TYPE_BACK.includes( details.type.toString().trim())){            
                details.errorMessage.push('only_chip_id_card');
            }
        }else{
            //rules for Shinhan
            // NhacVB R3360 - Add rules issue_date_prob
            if (
                Number(details.issue_date_prob) <=
                Number(EKYC_ISSSUE_DATE_PROB)  
            ) { 
                details.errorMessage.push('back_image_issue_date_invalid'); 
            }
            if (
                Number(details.issue_loc_prob) <=
                Number(process.env.EKYC_ISSUE_LOC_PROB)
            ) {
                details.errorMessage.push('issue_loc_prob_low');
            }
            // if (details.checking_result.edited_result === '1') {
            //     details.errorMessage.push('back_image_id_edited');
            // }
            if (
                details.checking_result.check_photocopied_result !== '0'
            ) {
                details.errorMessage.push('back_image_id_photo');
            }
            if (details.checking_result.corner_cut_result !== '0') {
                details.errorMessage.push('back_image_id_corner_cut');
            }
            if (Number(details.features_prob) <= Number(process.env.EKYC_FEATURES_PROB)) {
                details.errorMessage.push('features_is_blurred');
            }
            if (details.mrz_prob !== 'N/A' && Number(details.mrz_prob) <= Number(process.env.EKYC_MRZ_PROB)) {
                details.errorMessage.push('mrz_code_is_blurred');
            }
            // Nhacvb-7001
            // NhacVB-7282 TYPE_BACK
            if( !TYPE_BACK.includes( details.type.toString().trim())){            
                details.errorMessage.push('only_chip_id_card');
            }
        }
        details.errorMessage = uniq(details.errorMessage)
        if (details.errorMessage.length) {
            details.errorCode = 1;
        }
        return obj;
    };

    private checkEkycStep3 = (SecCode: string, objRaw: any): any => {
        const obj = cloneDeep(objRaw);
        obj.errorCode = 0;
        obj.errorMessage = [];

        if (obj.code === '301') {
            obj.errorCode = 1;
            obj.errorMessage = ['face_in_video_is_spoof'];
            return obj;
        }
        if (obj.code === '303') {
            obj.errorCode = 1;
            obj.errorMessage = ['video_face_not_match'];
            return obj;
        }
        if (obj.code === '406') {
            obj.errorCode = 1;
            obj.errorMessage = ['face_video_covered_or_too_dark_light'];
            return obj;
        }
        if (obj.code === '409') {
            obj.errorCode = 1;
            obj.errorMessage = ['video_is_too_short'];
            return obj;
        }
        if (obj.code === '422') {
            obj.errorCode = 1;
            obj.errorMessage = ['no_frontal_face_in_video'];
            return obj;
        }
        if (obj.code === '423') {
            obj.errorCode = 1;
            obj.errorMessage = ['face_is_out_of_frame_during_video'];
            return obj;
        }

        if (!obj.liveness) {
            obj.errorCode = 1;
            obj.errorMessage = ['video_face_fpt_cannot_detect'];
            return obj;
        }
        if (obj.liveness.is_live !== 'true') {
            obj.errorCode = 1;
            obj.errorMessage.push('video_face_not_match');
            return obj;
        }
        if (!obj.face_match) {
            obj.errorCode = 1;
            obj.errorMessage = ['video_face_not_match'];
            return obj;
        }
        if (obj.face_match.similarity < Number(process.env.EKYC_SIMILARITY)) {
            obj.errorCode = 1;
            obj.errorMessage.push('video_face_not_match');
            return obj;
        }

        if (obj.errorMessage.length) {
            obj.errorCode = 1;
        }
        return obj;
    };

    private checkEkycStep3FaceMatch = (
        SecCode: string,
        objRaw: any,
    ): any => {
        const obj = cloneDeep(objRaw);
        if (obj.code !== '200') {
            obj.errorCode = 1;
            if (obj.code === '409') {
                obj.errorMessage = ['Has_more_or_less_2_faces'];
            } else if (obj.code === '408') {
                obj.errorMessage = ['image_is_not_correct'];
            } else {
                obj.errorMessage = ['no_faces_detected'];
            }
            return obj;
        }
        obj.errorCode = 0;
        obj.errorMessage = [];
        if (!obj.data.isMatch) {
            obj.errorMessage = ['image_face_not_match'];
        }
        if (obj.errorMessage.length) {
            obj.errorCode = 1;
        }
        return obj;
    };

    public resizeImage = async (url: string, resize?: number) => {
        try {
            const resizedImageBuf = await sharp(url, {
                failOnError: false,
            })
                .resize(resize || 1000)
                .withMetadata()
                .toFormat('jpeg', { mozjpeg: true })
                .toBuffer();

            return {
                base64: `${resizedImageBuf.toString('base64')}`,
                buffer: resizedImageBuf,
            };
        } catch (error) {
            logger.error(
                `SERVER_ERROR: resizeImage sharp url(${url})`,
                { error },
            );
            return {
                base64: '',
                buffer: '',
            };
        }
    };

    public writeToFile = (
        filePath: string,
        buffer: string | Buffer,
    ): Promise<boolean> => {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(filePath);
            file.write(buffer);
            file.end();
            file.on('finish', () => {
                resolve(true);
            }); // not sure why you want to pass a boolean
            file.on('error', reject); // don't forget this!
        });
    };

    public getLastestDataReport = () => {
        this.pathReport = path.join(
            process.cwd(),
            `/report/data_${process.env.PORT}_${
                process.env.SERVER
            }_${moment().format('DDMMYYYY')}.json`,
        );
        fs.readFile(this.pathReport, (err, data) => {
            if (err) {
                return;
            }
            this.dataReport = JSON.parse(data.toString());
            console.log('getLastestDataReport', data.toString());
        });

        this.pathReportOther = path.join(
            process.cwd(),
            `/report/data_other_${process.env.PORT}_${
                process.env.SERVER
            }_${moment().format('DDMMYYYY')}`,
        );
        fs.readFile(this.pathReportOther, (err, data) => {
            if (err) {
                return;
            }
            this.dataReportOther = JSON.parse(data.toString());
            console.log('getLastestDataReportOther', data.toString());
        });
    };

    public logDataReport = () => {
        fs.writeFile(
            this.pathReport,
            JSON.stringify(this.dataReport),
            (err) => {},
        );
    };

    public logDataReportOther = () => {
        fs.writeFile(
            this.pathReportOther,
            JSON.stringify(this.dataReportOther),
            (err) => {},
        );
    };
}
const theInstance = new globalService();
export default theInstance;