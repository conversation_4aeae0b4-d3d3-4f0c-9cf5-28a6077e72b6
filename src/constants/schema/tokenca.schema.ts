import { celebrate, Joi, errors, Segments } from 'celebrate';

export default {
  upload: {
      [Segments.BODY]: {
        filename: Joi.string().required(),
        base64: Joi.string().required(),
        // userId: Joi.string().required(),
        // file: Joi.required(),
        // size: Joi.number().required(),
        // step: Joi.string().required(),
        // fileName: Joi.string().required()
      },
  },
};