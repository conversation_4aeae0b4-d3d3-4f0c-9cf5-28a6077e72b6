import { celebrate, Joi, errors, Segments } from 'celebrate';

export default {
    upload: {
        [Segments.BODY]: {
          // sec: Joi.string().required(),
          // userId: Joi.string().required(),
          // file: Joi.required(),
          // size: Joi.number().required(),
          // step: Joi.string().required(),
          // fileName: Joi.string().required()
        },
    },
    delete: {
      [Segments.BODY]: {
        files: Joi.array().required()
      }
    }
};